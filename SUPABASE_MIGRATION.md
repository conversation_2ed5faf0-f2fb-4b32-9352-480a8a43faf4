# Firebase to Supabase Migration Guide

## Overview
This document outlines the complete migration from Firebase to Supabase for the MindAssistant Android application.

## Migration Status: ✅ COMPLETED

### What Was Changed:

#### 1. Dependencies Updated
- ✅ Removed Firebase dependencies (`firebase-auth`, `firebase-firestore`)
- ✅ Added Supabase Kotlin dependencies (`auth-kt`, `postgrest-kt`, `realtime-kt`, `storage-kt`)
- ✅ Added Ktor client for Android
- ✅ Added Kotlin serialization plugin

#### 2. Authentication System
- ✅ Migrated `AuthManager` from Firebase Auth to Supabase Auth
- ✅ Updated `AuthViewModel` to use Supabase UserInfo instead of FirebaseUser
- ✅ Updated auth state flows and authentication methods

#### 3. Data Models
- ✅ Removed Firebase `@DocumentId` annotations
- ✅ Added Kotlinx `@Serializable` annotations
- ✅ Updated all data classes: `TodoItem`, `DueReminder`, `PaymentRecord`, `UserProfile`

#### 4. Database Services
- ✅ Created new Supabase services: `UserProfileService`, `TodoService`, `DueReminderService`
- ✅ Implemented real-time subscriptions using Supabase Realtime
- ✅ Updated `SyncRepository` to use Supabase services

#### 5. Dependency Injection
- ✅ Added Hilt support with `@HiltAndroidApp`
- ✅ Created `SupabaseModule` for dependency injection
- ✅ Created `DatabaseModule` for Room database injection
- ✅ Updated ViewModels to use Hilt injection

#### 6. Configuration
- ✅ Removed `google-services.json`
- ✅ Removed Google Services plugin
- ✅ Added Supabase configuration in `SupabaseModule`

## Required Supabase Database Schema

You need to create the following tables in your Supabase database:

### 1. User Profiles Table
```sql
CREATE TABLE user_profiles (
    uid TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    email TEXT NOT NULL,
    mobile TEXT NOT NULL,
    occupation TEXT NOT NULL,
    blood_group TEXT,
    upazila TEXT NOT NULL,
    district TEXT NOT NULL,
    created_at BIGINT NOT NULL,
    sync_frequency TEXT NOT NULL DEFAULT 'WEEKLY',
    auto_sync_enabled BOOLEAN NOT NULL DEFAULT true,
    sync_only_on_wifi BOOLEAN NOT NULL DEFAULT true,
    last_sync_time BIGINT NOT NULL DEFAULT 0,
    sync_notifications_enabled BOOLEAN NOT NULL DEFAULT true
);
```

### 2. Todos Table
```sql
CREATE TABLE todos (
    id TEXT PRIMARY KEY,
    user_id TEXT NOT NULL,
    title TEXT NOT NULL,
    description TEXT NOT NULL,
    is_completed BOOLEAN NOT NULL DEFAULT false,
    priority TEXT NOT NULL DEFAULT 'MEDIUM',
    category TEXT NOT NULL DEFAULT 'PERSONAL',
    due_date BIGINT,
    reminder_date BIGINT,
    tags TEXT[] DEFAULT '{}',
    created_at BIGINT NOT NULL,
    updated_at BIGINT NOT NULL,
    completed_at BIGINT,
    FOREIGN KEY (user_id) REFERENCES user_profiles(uid)
);
```

### 3. Due Reminders Table
```sql
CREATE TABLE due_reminders (
    id TEXT PRIMARY KEY,
    user_id TEXT NOT NULL,
    name TEXT NOT NULL,
    mobile_no TEXT NOT NULL,
    due_amount DOUBLE PRECISION NOT NULL DEFAULT 0.0,
    original_amount DOUBLE PRECISION NOT NULL DEFAULT 0.0,
    due_date BIGINT NOT NULL,
    next_reminder_date BIGINT NOT NULL,
    repeat TEXT NOT NULL DEFAULT 'NONE',
    note TEXT NOT NULL DEFAULT '',
    is_completed BOOLEAN NOT NULL DEFAULT false,
    created_at BIGINT NOT NULL,
    updated_at BIGINT NOT NULL,
    FOREIGN KEY (user_id) REFERENCES user_profiles(uid)
);
```

### 4. Payment Records Table
```sql
CREATE TABLE payment_records (
    id TEXT PRIMARY KEY,
    due_reminder_id TEXT NOT NULL,
    user_id TEXT NOT NULL,
    amount DOUBLE PRECISION NOT NULL DEFAULT 0.0,
    payment_date BIGINT NOT NULL,
    note TEXT NOT NULL DEFAULT '',
    payment_method TEXT NOT NULL DEFAULT 'CASH',
    created_at BIGINT NOT NULL,
    FOREIGN KEY (user_id) REFERENCES user_profiles(uid),
    FOREIGN KEY (due_reminder_id) REFERENCES due_reminders(id)
);
```

### 5. Enable Row Level Security (RLS)
```sql
-- Enable RLS on all tables
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE todos ENABLE ROW LEVEL SECURITY;
ALTER TABLE due_reminders ENABLE ROW LEVEL SECURITY;
ALTER TABLE payment_records ENABLE ROW LEVEL SECURITY;

-- Create policies for user_profiles
CREATE POLICY "Users can view own profile" ON user_profiles FOR SELECT USING (auth.uid() = uid);
CREATE POLICY "Users can insert own profile" ON user_profiles FOR INSERT WITH CHECK (auth.uid() = uid);
CREATE POLICY "Users can update own profile" ON user_profiles FOR UPDATE USING (auth.uid() = uid);

-- Create policies for todos
CREATE POLICY "Users can view own todos" ON todos FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert own todos" ON todos FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update own todos" ON todos FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can delete own todos" ON todos FOR DELETE USING (auth.uid() = user_id);

-- Create policies for due_reminders
CREATE POLICY "Users can view own reminders" ON due_reminders FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert own reminders" ON due_reminders FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update own reminders" ON due_reminders FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can delete own reminders" ON due_reminders FOR DELETE USING (auth.uid() = user_id);

-- Create policies for payment_records
CREATE POLICY "Users can view own payments" ON payment_records FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert own payments" ON payment_records FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update own payments" ON payment_records FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can delete own payments" ON payment_records FOR DELETE USING (auth.uid() = user_id);
```

## Configuration Steps

### 1. Update Supabase Configuration
In `app/src/main/java/me/fazlerabbie/mindasistant/supabase/SupabaseModule.kt`, replace:
```kotlin
supabaseUrl = "YOUR_SUPABASE_URL", // Replace with your actual Supabase URL
supabaseKey = "YOUR_SUPABASE_ANON_KEY" // Replace with your actual Supabase anon key
```

### 2. Enable Realtime (Optional)
If you want real-time updates, enable Realtime for your tables in the Supabase dashboard.

## Testing the Migration

1. ✅ Build the project successfully
2. ✅ Test user authentication (sign up, sign in, sign out)
3. ✅ Test data operations (create, read, update, delete)
4. ✅ Test real-time updates
5. ✅ Test offline functionality with local Room database

## Benefits of Migration

1. **Cost Effective**: Supabase is generally more cost-effective than Firebase
2. **Open Source**: Full transparency and control
3. **PostgreSQL**: More powerful database with SQL queries
4. **Real-time**: Built-in real-time subscriptions
5. **Self-hosting**: Option to self-host if needed

## Notes

- The local Room database remains unchanged and continues to provide offline-first functionality
- All existing offline features continue to work
- The sync mechanism now uses Supabase instead of Firebase
- Real-time updates are now powered by Supabase Realtime instead of Firestore listeners

## Migration Complete! 🎉

Your Android app has been successfully migrated from Firebase to Supabase. Make sure to:
1. Create the database schema in Supabase
2. Update the configuration with your Supabase credentials
3. Test all functionality thoroughly
