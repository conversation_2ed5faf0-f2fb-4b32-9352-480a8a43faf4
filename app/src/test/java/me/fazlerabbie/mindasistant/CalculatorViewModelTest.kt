package me.fazlerabbie.mindasistant

import kotlinx.coroutines.test.runTest
import me.fazlerabbie.mindasistant.ui.screens.features.calculator.CalculatorViewModel
import me.fazlerabbie.mindasistant.ui.screens.features.calculator.Operation
import org.junit.Assert.*
import org.junit.Before
import org.junit.Test

class CalculatorViewModelTest {

    private lateinit var viewModel: CalculatorViewModel

    @Before
    fun setup() {
        viewModel = CalculatorViewModel()
    }

    @Test
    fun `initial state should be zero`() = runTest {
        val state = viewModel.state.value
        assertEquals("0", state.display)
        assertEquals(0.0, state.previousValue, 0.0)
        assertNull(state.operation)
        assertFalse(state.waitingForOperand)
        assertTrue(state.history.isEmpty())
        assertNull(state.error)
    }

    @Test
    fun `number input should update display`() = runTest {
        viewModel.onNumberClick("5")
        assertEquals("5", viewModel.state.value.display)
        
        viewModel.onNumberClick("3")
        assertEquals("53", viewModel.state.value.display)
    }

    @Test
    fun `decimal input should work correctly`() = runTest {
        viewModel.onNumberClick("5")
        viewModel.onDecimalClick()
        viewModel.onNumberClick("2")
        assertEquals("5.2", viewModel.state.value.display)
    }

    @Test
    fun `basic addition should work`() = runTest {
        viewModel.onNumberClick("5")
        viewModel.onOperationClick(Operation.ADD)
        viewModel.onNumberClick("3")
        viewModel.onEqualsClick()

        assertEquals("8", viewModel.state.value.display)
        assertTrue(viewModel.state.value.history.isNotEmpty())
        assertTrue(viewModel.state.value.history.last().contains("5+3 = 8"))
    }

    @Test
    fun `basic subtraction should work`() = runTest {
        viewModel.onNumberClick("1")
        viewModel.onNumberClick("0")
        viewModel.onOperationClick(Operation.SUBTRACT)
        viewModel.onNumberClick("3")
        viewModel.onEqualsClick()
        
        assertEquals("7", viewModel.state.value.display)
    }

    @Test
    fun `basic multiplication should work`() = runTest {
        viewModel.onNumberClick("6")
        viewModel.onOperationClick(Operation.MULTIPLY)
        viewModel.onNumberClick("7")
        viewModel.onEqualsClick()
        
        assertEquals("42", viewModel.state.value.display)
    }

    @Test
    fun `basic division should work`() = runTest {
        viewModel.onNumberClick("1")
        viewModel.onNumberClick("5")
        viewModel.onOperationClick(Operation.DIVIDE)
        viewModel.onNumberClick("3")
        viewModel.onEqualsClick()
        
        assertEquals("5", viewModel.state.value.display)
    }

    @Test
    fun `division by zero should show error`() = runTest {
        viewModel.onNumberClick("5")
        viewModel.onOperationClick(Operation.DIVIDE)
        viewModel.onNumberClick("0")
        viewModel.onEqualsClick()
        
        assertNotNull(viewModel.state.value.error)
        assertTrue(viewModel.state.value.error!!.contains("divide by zero"))
    }

    @Test
    fun `percentage should work correctly`() = runTest {
        viewModel.onNumberClick("5")
        viewModel.onNumberClick("0")
        viewModel.onPercentageClick()

        // After percentage click, the expression should show 50%
        assertEquals("50%", viewModel.state.value.currentExpression)

        // When equals is pressed, it should calculate to 0.5
        viewModel.onEqualsClick()
        assertEquals("0.5", viewModel.state.value.display)
    }

    @Test
    fun `contextual percentage should work correctly`() = runTest {
        // Test 100-10% = 90
        viewModel.onNumberClick("1")
        viewModel.onNumberClick("0")
        viewModel.onNumberClick("0")
        viewModel.onOperationClick(Operation.SUBTRACT)
        viewModel.onNumberClick("1")
        viewModel.onNumberClick("0")
        viewModel.onPercentageClick()

        // Should show 100-10% in expression
        assertEquals("100-10%", viewModel.state.value.currentExpression)

        viewModel.onEqualsClick()
        assertEquals("90", viewModel.state.value.display)
        assertTrue(viewModel.state.value.history.last().contains("100-10% = 90"))
    }

    @Test
    fun `contextual percentage with addition should work`() = runTest {
        // Test 200+15% = 230
        viewModel.onNumberClick("2")
        viewModel.onNumberClick("0")
        viewModel.onNumberClick("0")
        viewModel.onOperationClick(Operation.ADD)
        viewModel.onNumberClick("1")
        viewModel.onNumberClick("5")
        viewModel.onPercentageClick()

        // Should show 200+15% in expression
        assertEquals("200+15%", viewModel.state.value.currentExpression)

        viewModel.onEqualsClick()
        assertEquals("230", viewModel.state.value.display)
        assertTrue(viewModel.state.value.history.last().contains("200+15% = 230"))
    }

    @Test
    fun `clear should reset display but keep history`() = runTest {
        viewModel.onNumberClick("5")
        viewModel.onOperationClick(Operation.ADD)
        viewModel.onNumberClick("3")
        viewModel.onEqualsClick()
        
        val historySize = viewModel.state.value.history.size
        
        viewModel.onClearClick()
        
        assertEquals("0", viewModel.state.value.display)
        assertEquals(historySize, viewModel.state.value.history.size)
    }

    @Test
    fun `all clear should reset everything`() = runTest {
        viewModel.onNumberClick("5")
        viewModel.onOperationClick(Operation.ADD)
        viewModel.onNumberClick("3")
        viewModel.onEqualsClick()
        
        viewModel.onAllClearClick()
        
        assertEquals("0", viewModel.state.value.display)
        assertTrue(viewModel.state.value.history.isEmpty())
        assertNull(viewModel.state.value.operation)
    }

    @Test
    fun `delete should remove last digit`() = runTest {
        viewModel.onNumberClick("1")
        viewModel.onNumberClick("2")
        viewModel.onNumberClick("3")
        
        viewModel.onDeleteClick()
        assertEquals("12", viewModel.state.value.display)
        
        viewModel.onDeleteClick()
        assertEquals("1", viewModel.state.value.display)
        
        viewModel.onDeleteClick()
        assertEquals("0", viewModel.state.value.display)
    }

    @Test
    fun `history should be maintained correctly`() = runTest {
        // Perform multiple calculations
        viewModel.onNumberClick("2")
        viewModel.onOperationClick(Operation.ADD)
        viewModel.onNumberClick("3")
        viewModel.onEqualsClick()

        viewModel.onNumberClick("5")
        viewModel.onOperationClick(Operation.MULTIPLY)
        viewModel.onNumberClick("4")
        viewModel.onEqualsClick()

        val history = viewModel.state.value.history
        assertEquals(2, history.size)
        assertTrue(history[0].contains("2+3 = 5"))
        assertTrue(history[1].contains("5×4 = 20"))
    }
}
