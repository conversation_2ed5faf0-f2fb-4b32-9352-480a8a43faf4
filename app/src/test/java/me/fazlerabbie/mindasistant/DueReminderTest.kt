package me.fazlerabbie.mindasistant

import me.fazlerabbie.mindasistant.data.model.DueReminder
import me.fazlerabbie.mindasistant.data.model.RepeatType
import org.junit.Test
import org.junit.Assert.*
import java.util.*

class DueReminderTest {
    
    @Test
    fun testDueReminderCreation() {
        val reminder = DueReminder(
            id = "test-id",
            userId = "user-123",
            name = "<PERSON>",
            mobileNo = "01234567890",
            dueAmount = 5000.0,
            originalAmount = 5000.0,
            dueDate = System.currentTimeMillis(),
            repeat = RepeatType.MONTHLY,
            note = "Test reminder"
        )
        
        assertEquals("<PERSON>", reminder.name)
        assertEquals(5000.0, reminder.dueAmount, 0.0)
        assertEquals(RepeatType.MONTHLY, reminder.repeat)
        assertFalse(reminder.isCompleted)
    }
    
    @Test
    fun testIsDueToday() {
        val today = Calendar.getInstance().apply {
            set(Calendar.HOUR_OF_DAY, 12)
            set(Calendar.MINUTE, 0)
            set(Calendar.SECOND, 0)
            set(Calendar.MILLISECOND, 0)
        }.timeInMillis
        
        val reminder = DueReminder(
            dueDate = today,
            name = "Test",
            userId = "user-123"
        )
        
        assertTrue(reminder.isDueToday())
    }
    
    @Test
    fun testIsOverdue() {
        val yesterday = Calendar.getInstance().apply {
            add(Calendar.DAY_OF_MONTH, -2)
            set(Calendar.HOUR_OF_DAY, 12)
            set(Calendar.MINUTE, 0)
            set(Calendar.SECOND, 0)
            set(Calendar.MILLISECOND, 0)
        }.timeInMillis
        
        val reminder = DueReminder(
            dueDate = yesterday,
            name = "Test",
            userId = "user-123",
            isCompleted = false
        )
        
        assertTrue(reminder.isOverdue())
    }
    
    @Test
    fun testCalculateNextReminderDate() {
        val currentDate = Calendar.getInstance().apply {
            set(2024, Calendar.JANUARY, 15)
            set(Calendar.HOUR_OF_DAY, 0)
            set(Calendar.MINUTE, 0)
            set(Calendar.SECOND, 0)
            set(Calendar.MILLISECOND, 0)
        }.timeInMillis
        
        val reminder = DueReminder(
            dueDate = currentDate,
            repeat = RepeatType.MONTHLY,
            name = "Test",
            userId = "user-123"
        )
        
        val nextDate = reminder.calculateNextReminderDate()
        val expectedDate = Calendar.getInstance().apply {
            set(2024, Calendar.FEBRUARY, 15)
            set(Calendar.HOUR_OF_DAY, 0)
            set(Calendar.MINUTE, 0)
            set(Calendar.SECOND, 0)
            set(Calendar.MILLISECOND, 0)
        }.timeInMillis
        
        assertEquals(expectedDate, nextDate)
    }
    
    @Test
    fun testRemainingAmount() {
        val reminder = DueReminder(
            dueAmount = 3500.0,
            originalAmount = 5000.0,
            name = "Test",
            userId = "user-123"
        )
        
        assertEquals(3500.0, reminder.getRemainingAmount(), 0.0)
    }
}
