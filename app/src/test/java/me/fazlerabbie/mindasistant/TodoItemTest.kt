package me.fazlerabbie.mindasistant

import me.fazlerabbie.mindasistant.data.model.TodoCategory
import me.fazlerabbie.mindasistant.data.model.TodoItem
import me.fazlerabbie.mindasistant.data.model.TodoPriority
import org.junit.Test
import org.junit.Assert.*
import java.util.*

class TodoItemTest {
    
    @Test
    fun testTodoItemCreation() {
        val todoItem = TodoItem(
            id = "test-id",
            userId = "user-123",
            title = "Test Todo",
            description = "This is a test todo item",
            priority = TodoPriority.HIGH,
            category = TodoCategory.WORK,
            tags = listOf("test", "work", "important")
        )
        
        assertEquals("Test Todo", todoItem.title)
        assertEquals("This is a test todo item", todoItem.description)
        assertEquals(TodoPriority.HIGH, todoItem.priority)
        assertEquals(TodoCategory.WORK, todoItem.category)
        assertEquals(3, todoItem.tags.size)
        assertFalse(todoItem.isCompleted)
    }
    
    @Test
    fun testTodoItemIsOverdue() {
        val yesterday = System.currentTimeMillis() - (24 * 60 * 60 * 1000)
        val todoItem = TodoItem(
            id = "test-id",
            userId = "user-123",
            title = "Overdue Todo",
            dueDate = yesterday,
            isCompleted = false
        )
        
        assertTrue(todoItem.isOverdue())
    }
    
    @Test
    fun testTodoItemIsNotOverdueWhenCompleted() {
        val yesterday = System.currentTimeMillis() - (24 * 60 * 60 * 1000)
        val todoItem = TodoItem(
            id = "test-id",
            userId = "user-123",
            title = "Completed Todo",
            dueDate = yesterday,
            isCompleted = true
        )
        
        assertFalse(todoItem.isOverdue())
    }
    
    @Test
    fun testTodoItemIsDueToday() {
        val today = Calendar.getInstance().apply {
            set(Calendar.HOUR_OF_DAY, 12)
            set(Calendar.MINUTE, 0)
            set(Calendar.SECOND, 0)
            set(Calendar.MILLISECOND, 0)
        }.timeInMillis
        
        val todoItem = TodoItem(
            id = "test-id",
            userId = "user-123",
            title = "Today's Todo",
            dueDate = today
        )
        
        assertTrue(todoItem.isDueToday())
    }
    
    @Test
    fun testTodoItemIsDueTomorrow() {
        val tomorrow = Calendar.getInstance().apply {
            add(Calendar.DAY_OF_MONTH, 1)
            set(Calendar.HOUR_OF_DAY, 12)
            set(Calendar.MINUTE, 0)
            set(Calendar.SECOND, 0)
            set(Calendar.MILLISECOND, 0)
        }.timeInMillis
        
        val todoItem = TodoItem(
            id = "test-id",
            userId = "user-123",
            title = "Tomorrow's Todo",
            dueDate = tomorrow
        )
        
        assertTrue(todoItem.isDueTomorrow())
    }
    
    @Test
    fun testTodoItemIsUpcoming() {
        val nextWeek = System.currentTimeMillis() + (7 * 24 * 60 * 60 * 1000)
        val todoItem = TodoItem(
            id = "test-id",
            userId = "user-123",
            title = "Upcoming Todo",
            dueDate = nextWeek,
            isCompleted = false
        )
        
        assertTrue(todoItem.isUpcoming())
    }
    
    @Test
    fun testTodoItemGetDaysUntilDue() {
        val threeDaysFromNow = Calendar.getInstance().apply {
            add(Calendar.DAY_OF_MONTH, 3)
            set(Calendar.HOUR_OF_DAY, 12)
            set(Calendar.MINUTE, 0)
            set(Calendar.SECOND, 0)
            set(Calendar.MILLISECOND, 0)
        }.timeInMillis
        
        val todoItem = TodoItem(
            id = "test-id",
            userId = "user-123",
            title = "Future Todo",
            dueDate = threeDaysFromNow
        )
        
        val daysUntilDue = todoItem.getDaysUntilDue()
        assertEquals(3, daysUntilDue)
    }
    
    @Test
    fun testTodoItemWithNoDueDate() {
        val todoItem = TodoItem(
            id = "test-id",
            userId = "user-123",
            title = "No Due Date Todo",
            dueDate = null
        )
        
        assertFalse(todoItem.isOverdue())
        assertFalse(todoItem.isDueToday())
        assertFalse(todoItem.isDueTomorrow())
        assertFalse(todoItem.isUpcoming())
        assertNull(todoItem.getDaysUntilDue())
    }
    
    @Test
    fun testTodoPriorityColors() {
        assertEquals(0xFF16A34A, TodoPriority.LOW.colorValue)
        assertEquals(0xFFEA580C, TodoPriority.MEDIUM.colorValue)
        assertEquals(0xFFDC2626, TodoPriority.HIGH.colorValue)
        assertEquals(0xFF7C2D12, TodoPriority.URGENT.colorValue)
    }
    
    @Test
    fun testTodoCategoryColors() {
        assertEquals(0xFF2563EB, TodoCategory.PERSONAL.colorValue)
        assertEquals(0xFF7C3AED, TodoCategory.WORK.colorValue)
        assertEquals(0xFF059669, TodoCategory.SHOPPING.colorValue)
        assertEquals(0xFFDC2626, TodoCategory.HEALTH.colorValue)
        assertEquals(0xFFDB2777, TodoCategory.EDUCATION.colorValue)
        assertEquals(0xFFEA580C, TodoCategory.FINANCE.colorValue)
        assertEquals(0xFF0891B2, TodoCategory.TRAVEL.colorValue)
        assertEquals(0xFF6B7280, TodoCategory.OTHER.colorValue)
    }
    
    @Test
    fun testTodoItemWithTags() {
        val tags = listOf("urgent", "meeting", "client", "presentation")
        val todoItem = TodoItem(
            id = "test-id",
            userId = "user-123",
            title = "Tagged Todo",
            tags = tags
        )
        
        assertEquals(4, todoItem.tags.size)
        assertTrue(todoItem.tags.contains("urgent"))
        assertTrue(todoItem.tags.contains("meeting"))
        assertTrue(todoItem.tags.contains("client"))
        assertTrue(todoItem.tags.contains("presentation"))
    }
    
    @Test
    fun testTodoItemTimestamps() {
        val currentTime = System.currentTimeMillis()
        val todoItem = TodoItem(
            id = "test-id",
            userId = "user-123",
            title = "Timestamp Todo",
            createdAt = currentTime,
            updatedAt = currentTime
        )
        
        assertEquals(currentTime, todoItem.createdAt)
        assertEquals(currentTime, todoItem.updatedAt)
        assertNull(todoItem.completedAt)
    }
    
    @Test
    fun testCompletedTodoItem() {
        val completedTime = System.currentTimeMillis()
        val todoItem = TodoItem(
            id = "test-id",
            userId = "user-123",
            title = "Completed Todo",
            isCompleted = true,
            completedAt = completedTime
        )
        
        assertTrue(todoItem.isCompleted)
        assertEquals(completedTime, todoItem.completedAt)
    }
}
