package me.fazlerabbie.mindasistant.ui.components

import androidx.compose.ui.test.*
import androidx.compose.ui.test.junit4.createComposeRule
import androidx.test.ext.junit.runners.AndroidJUnit4
import me.fazlerabbie.mindasistant.ui.theme.MindAsistantTheme
import org.junit.Rule
import org.junit.Test
import org.junit.runner.RunWith

@RunWith(AndroidJUnit4::class)
class BreakingNewsTest {

    @get:Rule
    val composeTestRule = createComposeRule()

    @Test
    fun breakingNewsSection_displaysCorrectly() {
        composeTestRule.setContent {
            MindAsistantTheme {
                BreakingNewsSection()
            }
        }

        // Verify the Breaking News label is displayed
        composeTestRule
            .onNodeWithText("BREAKING")
            .assertIsDisplayed()

        // Verify that news content is displayed (using BasicText now)
        composeTestRule
            .onNodeWithText("আজ বাংলাদেশ সরকার সিদ্ধান্ত নিয়েছে", substring = true)
            .assertExists()
    }

    @Test
    fun breakingNewsSection_hasCorrectStructure() {
        composeTestRule.setContent {
            MindAsistantTheme {
                BreakingNewsSection()
            }
        }

        // Verify the component structure
        composeTestRule
            .onNodeWithText("BREAKING")
            .assertIsDisplayed()
    }
}
