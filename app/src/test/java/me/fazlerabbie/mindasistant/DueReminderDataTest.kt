package me.fazlerabbie.mindasistant

import me.fazlerabbie.mindasistant.data.model.DueReminder
import me.fazlerabbie.mindasistant.data.model.PaymentRecord
import me.fazlerabbie.mindasistant.data.model.PaymentMethod
import me.fazlerabbie.mindasistant.data.model.RepeatType
import org.junit.Test
import org.junit.Assert.*

class DueReminderDataTest {
    
    @Test
    fun testDueReminderDataIntegrity() {
        // Test creating a due reminder with all fields
        val reminder = DueReminder(
            id = "test-123",
            userId = "user-456",
            name = "<PERSON>",
            mobileNo = "01712345678",
            dueAmount = 5000.0,
            originalAmount = 5000.0,
            dueDate = System.currentTimeMillis(),
            nextReminderDate = System.currentTimeMillis(),
            repeat = RepeatType.MONTHLY,
            note = "Monthly loan payment",
            isCompleted = false,
            createdAt = System.currentTimeMillis(),
            updatedAt = System.currentTimeMillis()
        )
        
        // Verify all fields are set correctly
        assertEquals("test-123", reminder.id)
        assertEquals("user-456", reminder.userId)
        assertEquals("<PERSON>", reminder.name)
        assertEquals("01712345678", reminder.mobileNo)
        assertEquals(5000.0, reminder.dueAmount, 0.0)
        assertEquals(5000.0, reminder.originalAmount, 0.0)
        assertEquals(RepeatType.MONTHLY, reminder.repeat)
        assertEquals("Monthly loan payment", reminder.note)
        assertFalse(reminder.isCompleted)
        
        println("✅ Due reminder data integrity test passed")
    }
    
    @Test
    fun testPaymentRecordDataIntegrity() {
        // Test creating a payment record
        val payment = PaymentRecord(
            id = "payment-123",
            dueReminderId = "reminder-456",
            userId = "user-789",
            amount = 1000.0,
            paymentDate = System.currentTimeMillis(),
            note = "Partial payment",
            paymentMethod = PaymentMethod.MOBILE_BANKING,
            createdAt = System.currentTimeMillis()
        )
        
        // Verify all fields are set correctly
        assertEquals("payment-123", payment.id)
        assertEquals("reminder-456", payment.dueReminderId)
        assertEquals("user-789", payment.userId)
        assertEquals(1000.0, payment.amount, 0.0)
        assertEquals("Partial payment", payment.note)
        assertEquals(PaymentMethod.MOBILE_BANKING, payment.paymentMethod)
        
        println("✅ Payment record data integrity test passed")
    }
    
    @Test
    fun testDueReminderBusinessLogic() {
        val currentTime = System.currentTimeMillis()
        
        // Test overdue reminder
        val overdueReminder = DueReminder(
            dueDate = currentTime - (2 * 24 * 60 * 60 * 1000), // 2 days ago
            isCompleted = false,
            name = "Overdue Test",
            userId = "user-123"
        )
        assertTrue("Should be overdue", overdueReminder.isOverdue())
        
        // Test completed reminder (should not be overdue)
        val completedReminder = DueReminder(
            dueDate = currentTime - (2 * 24 * 60 * 60 * 1000), // 2 days ago
            isCompleted = true,
            name = "Completed Test",
            userId = "user-123"
        )
        assertFalse("Completed reminder should not be overdue", completedReminder.isOverdue())
        
        println("✅ Due reminder business logic test passed")
    }
    
    @Test
    fun testDataValidation() {
        // Test that required fields are properly validated
        val validReminder = DueReminder(
            name = "Valid User",
            userId = "user-123",
            dueAmount = 1000.0,
            originalAmount = 1000.0
        )
        
        assertTrue("Name should not be empty", validReminder.name.isNotEmpty())
        assertTrue("User ID should not be empty", validReminder.userId.isNotEmpty())
        assertTrue("Due amount should be positive", validReminder.dueAmount > 0)
        assertTrue("Original amount should be positive", validReminder.originalAmount > 0)
        
        println("✅ Data validation test passed")
    }
    
    @Test
    fun testPaymentMethodEnum() {
        // Test all payment methods
        val methods = PaymentMethod.values()
        assertTrue("Should have payment methods", methods.isNotEmpty())
        
        // Test specific methods
        assertEquals("Cash", PaymentMethod.CASH.displayName)
        assertEquals("Mobile Banking", PaymentMethod.MOBILE_BANKING.displayName)
        assertEquals("Bank Transfer", PaymentMethod.BANK_TRANSFER.displayName)
        
        println("✅ Payment method enum test passed")
    }
    
    @Test
    fun testRepeatTypeEnum() {
        // Test all repeat types
        val repeatTypes = RepeatType.values()
        assertTrue("Should have repeat types", repeatTypes.isNotEmpty())
        
        // Test specific types
        assertEquals("No Repeat", RepeatType.NONE.displayName)
        assertEquals("Monthly", RepeatType.MONTHLY.displayName)
        assertEquals("Weekly", RepeatType.WEEKLY.displayName)
        
        println("✅ Repeat type enum test passed")
    }
}
