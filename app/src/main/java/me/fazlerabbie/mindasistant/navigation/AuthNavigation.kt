package me.fazlerabbie.mindasistant.navigation

import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.navigation.NavHostController
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.rememberNavController
import me.fazlerabbie.mindasistant.auth.AuthState
import me.fazlerabbie.mindasistant.auth.AuthViewModel
import me.fazlerabbie.mindasistant.ui.components.MindAssistantLogo
import me.fazlerabbie.mindasistant.ui.screens.HomeScreen
import me.fazlerabbie.mindasistant.ui.screens.LoginScreen
import me.fazlerabbie.mindasistant.ui.screens.MainScreen
import me.fazlerabbie.mindasistant.ui.screens.SignUpScreen
import me.fazlerabbie.mindasistant.ui.screens.SplashScreen
import me.fazlerabbie.mindasistant.ui.screens.features.*

sealed class Screen(val route: String) {
    object Splash : Screen("splash")
    object Login : Screen("login")
    object SignUp : Screen("signup")
    object Main : Screen("main")
    object Home : Screen("home")
    object DueRemainder : Screen("due_remainder")
    object TodoList : Screen("todo_list")
    object Calculator : Screen("calculator")
    object Notepad : Screen("notepad")
    object TipsTricks : Screen("tips_tricks")
    object MedicineIndex : Screen("medicine_index")
    object FeatureRequest : Screen("feature_request")
    object LifeMoment : Screen("life_moment")
    object UserSettings : Screen("user_settings")
    object Profile : Screen("profile")
}

@Composable
fun AuthNavigation(
    navController: NavHostController = rememberNavController(),
    authViewModel: AuthViewModel = viewModel()
) {
    val authState by authViewModel.authState.collectAsState()

    NavHost(
        navController = navController,
        startDestination = Screen.Splash.route
    ) {
        composable(Screen.Splash.route) {
            SplashScreen(
                onSplashFinished = {
                    // Navigate based on auth state after splash
                    when (authState) {
                        is AuthState.Authenticated -> {
                            navController.navigate(Screen.Main.route) {
                                popUpTo(Screen.Splash.route) { inclusive = true }
                            }
                        }
                        is AuthState.Unauthenticated -> {
                            navController.navigate(Screen.Login.route) {
                                popUpTo(Screen.Splash.route) { inclusive = true }
                            }
                        }
                        is AuthState.Loading -> {
                            // Stay on splash if still loading
                            // This shouldn't happen as splash waits for auth state
                        }
                    }
                }
            )
        }
        composable(Screen.Login.route) {
            LoginScreen(
                onLoginSuccess = {
                    navController.navigate(Screen.Main.route) {
                        popUpTo(Screen.Login.route) { inclusive = true }
                    }
                },
                onSignUpClick = {
                    navController.navigate(Screen.SignUp.route)
                },
                authViewModel = authViewModel
            )
        }

        composable(Screen.SignUp.route) {
            SignUpScreen(
                onSignUpSuccess = {
                    navController.navigate(Screen.Main.route) {
                        popUpTo(Screen.Login.route) { inclusive = true }
                    }
                },
                onSignInClick = {
                    navController.popBackStack()
                },
                authViewModel = authViewModel
            )
        }

        composable(Screen.Main.route) {
            MainScreen(
                authViewModel = authViewModel,
                onNavigateToFeature = { route ->
                    navController.navigate(route)
                }
            )
        }

        composable(Screen.Home.route) {
            val authState by authViewModel.authState.collectAsState()
            val currentAuthState = authState
            val userProfile = if (currentAuthState is AuthState.Authenticated) {
                currentAuthState.userProfile
            } else null

            HomeScreen(
                userProfile = userProfile,
                authViewModel = authViewModel,
                onNavigateToFeature = { route ->
                    navController.navigate(route)
                }
            )
        }

        // Feature screens
        composable(Screen.DueRemainder.route) {
            DueRemainderScreen(
                onBackClick = { navController.popBackStack() }
            )
        }

        composable(Screen.TodoList.route) {
            TodoListScreen(
                onBackClick = { navController.popBackStack() }
            )
        }

        composable(Screen.Calculator.route) {
            CalculatorScreen(
                onBackClick = { navController.popBackStack() }
            )
        }

        composable(Screen.Notepad.route) {
            NotepadScreen(
                onBackClick = { navController.popBackStack() }
            )
        }

        composable(Screen.TipsTricks.route) {
            TipsAndTricksScreen(
                onBackClick = { navController.popBackStack() }
            )
        }

        composable(Screen.MedicineIndex.route) {
            MedicineIndexScreen(
                onBackClick = { navController.popBackStack() }
            )
        }

        composable(Screen.FeatureRequest.route) {
            FeatureRequestScreen(
                onBackClick = { navController.popBackStack() }
            )
        }

        composable(Screen.LifeMoment.route) {
            LifeMomentScreen(
                onBackClick = { navController.popBackStack() }
            )
        }

        composable(Screen.UserSettings.route) {
            UserSettingsScreenWrapper(
                onBackClick = { navController.popBackStack() }
            )
        }

        composable(Screen.Profile.route) {
            val authState by authViewModel.authState.collectAsState()
            val currentAuthState = authState
            val userProfile = if (currentAuthState is AuthState.Authenticated) {
                currentAuthState.userProfile
            } else null

            me.fazlerabbie.mindasistant.ui.screens.ProfileScreen(
                userProfile = userProfile,
                authViewModel = authViewModel,
                onNavigateToFeature = { route ->
                    navController.navigate(route)
                },
                onBackClick = { navController.popBackStack() }
            )
        }
    }
}
