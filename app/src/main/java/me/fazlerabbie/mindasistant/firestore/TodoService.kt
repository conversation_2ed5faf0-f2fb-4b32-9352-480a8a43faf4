package me.fazlerabbie.mindasistant.firestore

import com.google.firebase.firestore.FirebaseFirestore
import com.google.firebase.firestore.Query
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.callbackFlow
import kotlinx.coroutines.tasks.await
import me.fazlerabbie.mindasistant.data.model.TodoItem
import java.util.*

class TodoService {
    private val firestore = FirebaseFirestore.getInstance()
    private val todosCollection = firestore.collection("todos")

    /**
     * Get todos flow for real-time updates
     */
    fun getTodosFlow(userId: String): Flow<List<TodoItem>> = callbackFlow {
        val listener = todosCollection
            .whereEqualTo("userId", userId)
            .orderBy("createdAt", Query.Direction.DESCENDING)
            .addSnapshotListener { snapshot, error ->
                if (error != null) {
                    close(error)
                    return@addSnapshotListener
                }

                val todos = snapshot?.documents?.mapNotNull { doc ->
                    try {
                        doc.toObject(TodoItem::class.java)?.copy(id = doc.id)
                    } catch (e: Exception) {
                        null
                    }
                } ?: emptyList()

                trySend(todos)
            }

        awaitClose { listener.remove() }
    }

    /**
     * Get todos (one-time fetch)
     */
    suspend fun getTodos(userId: String): Result<List<TodoItem>> {
        return try {
            val snapshot = todosCollection
                .whereEqualTo("userId", userId)
                .orderBy("createdAt", Query.Direction.DESCENDING)
                .get()
                .await()

            val todos = snapshot.documents.mapNotNull { doc ->
                try {
                    doc.toObject(TodoItem::class.java)?.copy(id = doc.id)
                } catch (e: Exception) {
                    null
                }
            }

            Result.success(todos)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * Save todo item
     */
    suspend fun saveTodoItem(todoItem: TodoItem): Result<String> {
        return try {
            val todoId = if (todoItem.id.isEmpty()) {
                UUID.randomUUID().toString()
            } else {
                todoItem.id
            }

            val todoToSave = todoItem.copy(
                id = "", // Remove ID from the document data
                updatedAt = System.currentTimeMillis()
            )

            todosCollection.document(todoId)
                .set(todoToSave)
                .await()

            Result.success(todoId)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * Delete todo item
     */
    suspend fun deleteTodoItem(todoId: String): Result<Unit> {
        return try {
            todosCollection.document(todoId)
                .delete()
                .await()

            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * Toggle todo completion status
     */
    suspend fun toggleTodoCompletion(todoId: String, isCompleted: Boolean): Result<Unit> {
        return try {
            todosCollection.document(todoId)
                .update(
                    mapOf(
                        "isCompleted" to isCompleted,
                        "updatedAt" to System.currentTimeMillis()
                    )
                )
                .await()

            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * Delete completed todos
     */
    suspend fun deleteCompletedTodos(userId: String): Result<Unit> {
        return try {
            val completedTodos = todosCollection
                .whereEqualTo("userId", userId)
                .whereEqualTo("isCompleted", true)
                .get()
                .await()

            val batch = firestore.batch()
            completedTodos.documents.forEach { doc ->
                batch.delete(doc.reference)
            }
            batch.commit().await()

            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
}
