package me.fazlerabbie.mindasistant.firestore

import com.google.firebase.firestore.FirebaseFirestore
import com.google.firebase.firestore.Query
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.callbackFlow
import kotlinx.coroutines.tasks.await
import me.fazlerabbie.mindasistant.data.model.DueReminder
import me.fazlerabbie.mindasistant.data.model.PaymentRecord
import java.util.*

class DueReminderService {
    private val firestore = FirebaseFirestore.getInstance()
    private val dueRemindersCollection = firestore.collection("dueReminders")
    private val paymentRecordsCollection = firestore.collection("paymentRecords")

    /**
     * Get due reminders flow for real-time updates
     */
    fun getDueRemindersFlow(userId: String): Flow<List<DueReminder>> = callbackFlow {
        val listener = dueRemindersCollection
            .whereEqualTo("userId", userId)
            .orderBy("dueDate", Query.Direction.ASCENDING)
            .addSnapshotListener { snapshot, error ->
                if (error != null) {
                    close(error)
                    return@addSnapshotListener
                }

                val reminders = snapshot?.documents?.mapNotNull { doc ->
                    try {
                        doc.toObject(DueReminder::class.java)?.copy(id = doc.id)
                    } catch (e: Exception) {
                        null
                    }
                } ?: emptyList()

                trySend(reminders)
            }

        awaitClose { listener.remove() }
    }

    /**
     * Get due reminders (one-time fetch)
     */
    suspend fun getDueReminders(userId: String): Result<List<DueReminder>> {
        return try {
            val snapshot = dueRemindersCollection
                .whereEqualTo("userId", userId)
                .orderBy("dueDate", Query.Direction.ASCENDING)
                .get()
                .await()

            val reminders = snapshot.documents.mapNotNull { doc ->
                try {
                    doc.toObject(DueReminder::class.java)?.copy(id = doc.id)
                } catch (e: Exception) {
                    null
                }
            }

            Result.success(reminders)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * Save due reminder
     */
    suspend fun saveDueReminder(dueReminder: DueReminder): Result<String> {
        return try {
            val reminderId = if (dueReminder.id.isEmpty()) {
                UUID.randomUUID().toString()
            } else {
                dueReminder.id
            }

            val reminderToSave = dueReminder.copy(
                id = "", // Remove ID from the document data
                updatedAt = System.currentTimeMillis()
            )

            dueRemindersCollection.document(reminderId)
                .set(reminderToSave)
                .await()

            Result.success(reminderId)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * Update due reminder
     */
    suspend fun updateDueReminder(dueReminder: DueReminder): Result<Unit> {
        return try {
            val reminderToUpdate = dueReminder.copy(
                id = "", // Remove ID from the document data
                updatedAt = System.currentTimeMillis()
            )

            dueRemindersCollection.document(dueReminder.id)
                .set(reminderToUpdate)
                .await()

            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * Delete due reminder
     */
    suspend fun deleteDueReminder(reminderId: String): Result<Unit> {
        return try {
            dueRemindersCollection.document(reminderId)
                .delete()
                .await()

            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * Mark due reminder as completed
     */
    suspend fun markAsCompleted(reminderId: String): Result<Unit> {
        return try {
            dueRemindersCollection.document(reminderId)
                .update(
                    mapOf(
                        "isCompleted" to true,
                        "updatedAt" to System.currentTimeMillis()
                    )
                )
                .await()

            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * Save payment record
     */
    suspend fun savePaymentRecord(paymentRecord: PaymentRecord): Result<String> {
        return try {
            val recordId = if (paymentRecord.id.isEmpty()) {
                UUID.randomUUID().toString()
            } else {
                paymentRecord.id
            }

            val recordToSave = paymentRecord.copy(
                id = "", // Remove ID from the document data
                createdAt = System.currentTimeMillis()
            )

            paymentRecordsCollection.document(recordId)
                .set(recordToSave)
                .await()

            Result.success(recordId)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * Get payment records for a due reminder
     */
    suspend fun getPaymentRecords(dueReminderId: String): Result<List<PaymentRecord>> {
        return try {
            val snapshot = paymentRecordsCollection
                .whereEqualTo("dueReminderId", dueReminderId)
                .orderBy("createdAt", Query.Direction.DESCENDING)
                .get()
                .await()

            val records = snapshot.documents.mapNotNull { doc ->
                try {
                    doc.toObject(PaymentRecord::class.java)?.copy(id = doc.id)
                } catch (e: Exception) {
                    null
                }
            }

            Result.success(records)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * Delete payment record
     */
    suspend fun deletePaymentRecord(recordId: String): Result<Unit> {
        return try {
            paymentRecordsCollection.document(recordId)
                .delete()
                .await()

            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
}
