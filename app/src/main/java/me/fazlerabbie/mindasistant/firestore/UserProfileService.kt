package me.fazlerabbie.mindasistant.firestore

import com.google.firebase.firestore.FirebaseFirestore
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.callbackFlow
import kotlinx.coroutines.tasks.await
import me.fazlerabbie.mindasistant.auth.UserProfile

class UserProfileService {
    private val firestore = FirebaseFirestore.getInstance()
    private val userProfilesCollection = firestore.collection("userProfiles")

    /**
     * Get user profile flow for real-time updates
     */
    fun getUserProfileFlow(userId: String): Flow<UserProfile?> = callbackFlow {
        val listener = userProfilesCollection.document(userId)
            .addSnapshotListener { snapshot, error ->
                if (error != null) {
                    close(error)
                    return@addSnapshotListener
                }

                val userProfile = try {
                    snapshot?.toObject(UserProfile::class.java)
                } catch (e: Exception) {
                    null
                }

                trySend(userProfile)
            }

        awaitClose { listener.remove() }
    }

    /**
     * Get user profile (one-time fetch)
     */
    suspend fun getUserProfile(userId: String): Result<UserProfile?> {
        return try {
            val snapshot = userProfilesCollection.document(userId)
                .get()
                .await()

            val userProfile = if (snapshot.exists()) {
                try {
                    snapshot.toObject(UserProfile::class.java)
                } catch (e: Exception) {
                    null
                }
            } else {
                null
            }

            Result.success(userProfile)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * Save user profile
     */
    suspend fun saveUserProfile(userProfile: UserProfile): Result<Unit> {
        return try {
            userProfilesCollection.document(userProfile.uid)
                .set(userProfile)
                .await()

            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * Update user profile
     */
    suspend fun updateUserProfile(userProfile: UserProfile): Result<Unit> {
        return try {
            userProfilesCollection.document(userProfile.uid)
                .set(userProfile)
                .await()

            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * Update sync preferences
     */
    suspend fun updateSyncPreferences(
        userId: String,
        syncFrequency: String,
        autoSyncEnabled: Boolean,
        syncOnlyOnWifi: Boolean,
        syncNotificationsEnabled: Boolean
    ): Result<Unit> {
        return try {
            val updates = mapOf(
                "syncFrequency" to syncFrequency,
                "autoSyncEnabled" to autoSyncEnabled,
                "syncOnlyOnWifi" to syncOnlyOnWifi,
                "syncNotificationsEnabled" to syncNotificationsEnabled
            )

            userProfilesCollection.document(userId)
                .update(updates)
                .await()

            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * Update last sync time
     */
    suspend fun updateLastSyncTime(userId: String, lastSyncTime: Long): Result<Unit> {
        return try {
            userProfilesCollection.document(userId)
                .update("lastSyncTime", lastSyncTime)
                .await()

            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * Delete user profile
     */
    suspend fun deleteUserProfile(userId: String): Result<Unit> {
        return try {
            userProfilesCollection.document(userId)
                .delete()
                .await()

            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
}
