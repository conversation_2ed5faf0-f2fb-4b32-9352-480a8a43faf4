package me.fazlerabbie.mindasistant.sync

import android.app.NotificationChannel
import android.app.NotificationManager
import android.content.Context
import android.os.Build
import androidx.core.app.NotificationCompat
import androidx.core.app.NotificationManagerCompat
import me.fazlerabbie.mindasistant.R

object SyncNotificationManager {
    
    private const val SYNC_CHANNEL_ID = "sync_channel"
    private const val SYNC_SUCCESS_NOTIFICATION_ID = 1001
    private const val SYNC_ERROR_NOTIFICATION_ID = 1002
    
    fun createNotificationChannel(context: Context) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                SYNC_CHANNEL_ID,
                "Data Sync",
                NotificationManager.IMPORTANCE_LOW
            ).apply {
                description = "Notifications for data synchronization status"
                setShowBadge(false)
            }
            
            val notificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            notificationManager.createNotificationChannel(channel)
        }
    }
    
    fun showSyncSuccessNotification(context: Context) {
        val notification = NotificationCompat.Builder(context, SYNC_CHANNEL_ID)
            .setSmallIcon(R.drawable.ic_launcher_foreground) // You'll need to add this icon
            .setContentTitle("Sync Completed")
            .setContentText("Your data has been successfully synchronized")
            .setPriority(NotificationCompat.PRIORITY_LOW)
            .setAutoCancel(true)
            .build()
        
        with(NotificationManagerCompat.from(context)) {
            notify(SYNC_SUCCESS_NOTIFICATION_ID, notification)
        }
    }
    
    fun showSyncErrorNotification(context: Context, errorMessage: String) {
        val notification = NotificationCompat.Builder(context, SYNC_CHANNEL_ID)
            .setSmallIcon(R.drawable.ic_launcher_foreground) // You'll need to add this icon
            .setContentTitle("Sync Failed")
            .setContentText("Failed to sync data: $errorMessage")
            .setPriority(NotificationCompat.PRIORITY_DEFAULT)
            .setAutoCancel(true)
            .build()
        
        with(NotificationManagerCompat.from(context)) {
            notify(SYNC_ERROR_NOTIFICATION_ID, notification)
        }
    }
    
    fun showSyncInProgressNotification(context: Context): NotificationCompat.Builder {
        return NotificationCompat.Builder(context, SYNC_CHANNEL_ID)
            .setSmallIcon(R.drawable.ic_launcher_foreground)
            .setContentTitle("Syncing Data")
            .setContentText("Synchronizing your data with cloud...")
            .setPriority(NotificationCompat.PRIORITY_LOW)
            .setOngoing(true)
            .setProgress(0, 0, true)
    }
    
    fun cancelSyncNotifications(context: Context) {
        with(NotificationManagerCompat.from(context)) {
            cancel(SYNC_SUCCESS_NOTIFICATION_ID)
            cancel(SYNC_ERROR_NOTIFICATION_ID)
        }
    }
}
