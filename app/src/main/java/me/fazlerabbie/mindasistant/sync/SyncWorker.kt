package me.fazlerabbie.mindasistant.sync

import android.content.Context
import android.net.ConnectivityManager
import android.net.NetworkCapabilities
import androidx.work.CoroutineWorker
import androidx.work.WorkerParameters
import androidx.work.workDataOf
import com.google.firebase.auth.FirebaseAuth
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import me.fazlerabbie.mindasistant.data.local.MindAssistantDatabase
import me.fazlerabbie.mindasistant.data.repository.SyncRepository

class SyncWorker(
    context: Context,
    params: WorkerParameters
) : CoroutineWorker(context, params) {

    private val database = MindAssistantDatabase.getDatabase(context)
    private val syncRepository = SyncRepository(context)
    private val auth = FirebaseAuth.getInstance()

    override suspend fun doWork(): Result = withContext(Dispatchers.IO) {
        try {
            val currentUser = auth.currentUser
            if (currentUser == null) {
                return@withContext Result.failure(
                    workDataOf("error" to "User not authenticated")
                )
            }

            val userId = currentUser.uid
            
            // Check if user wants to sync only on WiFi
            val userProfile = database.userProfileDao().getUserProfile(userId)
            val syncOnlyOnWifi = userProfile?.syncOnlyOnWifi ?: true
            
            if (syncOnlyOnWifi && !isWifiConnected()) {
                return@withContext Result.failure(
                    workDataOf("error" to "WiFi not available and sync restricted to WiFi only")
                )
            }

            // Check if auto sync is enabled
            val autoSyncEnabled = userProfile?.autoSyncEnabled ?: true
            if (!autoSyncEnabled) {
                return@withContext Result.failure(
                    workDataOf("error" to "Auto sync is disabled")
                )
            }

            // Perform sync
            val syncResult = syncRepository.performFullSync(userId)
            
            if (syncResult.isSuccess) {
                // Update last sync time
                database.userProfileDao().updateLastSyncTime(userId, System.currentTimeMillis())
                
                // Send notification if enabled
                if (userProfile?.syncNotificationsEnabled == true) {
                    SyncNotificationManager.showSyncSuccessNotification(applicationContext)
                }
                
                Result.success(
                    workDataOf(
                        "message" to "Sync completed successfully",
                        "syncTime" to System.currentTimeMillis()
                    )
                )
            } else {
                // Send error notification if enabled
                if (userProfile?.syncNotificationsEnabled == true) {
                    SyncNotificationManager.showSyncErrorNotification(
                        applicationContext,
                        syncResult.exceptionOrNull()?.message ?: "Unknown error"
                    )
                }
                
                Result.failure(
                    workDataOf("error" to (syncResult.exceptionOrNull()?.message ?: "Sync failed"))
                )
            }
        } catch (e: Exception) {
            Result.failure(
                workDataOf("error" to e.message)
            )
        }
    }

    private fun isWifiConnected(): Boolean {
        val connectivityManager = applicationContext.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
        val network = connectivityManager.activeNetwork ?: return false
        val networkCapabilities = connectivityManager.getNetworkCapabilities(network) ?: return false
        return networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_WIFI)
    }
}
