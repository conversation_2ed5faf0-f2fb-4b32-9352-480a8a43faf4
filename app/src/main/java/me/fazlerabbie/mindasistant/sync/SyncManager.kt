package me.fazlerabbie.mindasistant.sync

import android.content.Context
import androidx.work.*
import me.fazlerabbie.mindasistant.auth.SyncFrequency
import me.fazlerabbie.mindasistant.data.local.MindAssistantDatabase
import java.util.concurrent.TimeUnit

class SyncManager(private val context: Context) {
    
    private val workManager = WorkManager.getInstance(context)
    private val database = MindAssistantDatabase.getDatabase(context)
    
    companion object {
        private const val SYNC_WORK_NAME = "mind_assistant_sync"
        private const val MANUAL_SYNC_WORK_NAME = "mind_assistant_manual_sync"
    }
    
    /**
     * Schedule periodic sync based on user's sync frequency preference
     */
    suspend fun scheduleSyncForUser(userId: String) {
        val userProfile = database.userProfileDao().getUserProfile(userId)
        val syncFrequency = userProfile?.syncFrequency ?: SyncFrequency.WEEKLY
        val autoSyncEnabled = userProfile?.autoSyncEnabled ?: true
        
        if (!autoSyncEnabled) {
            cancelPeriodicSync()
            return
        }
        
        val constraints = Constraints.Builder()
            .setRequiredNetworkType(
                if (userProfile?.syncOnlyOnWifi == true) {
                    NetworkType.UNMETERED // WiFi only
                } else {
                    NetworkType.CONNECTED // Any network
                }
            )
            .setRequiresBatteryNotLow(true)
            .build()
        
        val syncRequest = PeriodicWorkRequestBuilder<SyncWorker>(
            repeatInterval = syncFrequency.intervalMillis,
            repeatIntervalTimeUnit = TimeUnit.MILLISECONDS,
            flexTimeInterval = 1, // 1 hour flex time
            flexTimeIntervalUnit = TimeUnit.HOURS
        )
            .setConstraints(constraints)
            .setBackoffCriteria(
                BackoffPolicy.EXPONENTIAL,
                WorkRequest.MIN_BACKOFF_MILLIS,
                TimeUnit.MILLISECONDS
            )
            .addTag("sync")
            .addTag(userId)
            .build()
        
        workManager.enqueueUniquePeriodicWork(
            SYNC_WORK_NAME,
            ExistingPeriodicWorkPolicy.REPLACE,
            syncRequest
        )
    }
    
    /**
     * Trigger manual sync immediately
     */
    fun triggerManualSync(userId: String) {
        val constraints = Constraints.Builder()
            .setRequiredNetworkType(NetworkType.CONNECTED)
            .build()
        
        val manualSyncRequest = OneTimeWorkRequestBuilder<SyncWorker>()
            .setConstraints(constraints)
            .setBackoffCriteria(
                BackoffPolicy.EXPONENTIAL,
                WorkRequest.MIN_BACKOFF_MILLIS,
                TimeUnit.MILLISECONDS
            )
            .addTag("manual_sync")
            .addTag(userId)
            .build()
        
        workManager.enqueueUniqueWork(
            MANUAL_SYNC_WORK_NAME,
            ExistingWorkPolicy.REPLACE,
            manualSyncRequest
        )
    }
    
    /**
     * Cancel all sync work
     */
    fun cancelPeriodicSync() {
        workManager.cancelUniqueWork(SYNC_WORK_NAME)
    }
    
    /**
     * Cancel manual sync
     */
    fun cancelManualSync() {
        workManager.cancelUniqueWork(MANUAL_SYNC_WORK_NAME)
    }
    
    /**
     * Cancel all sync work for a specific user
     */
    fun cancelSyncForUser(userId: String) {
        workManager.cancelAllWorkByTag(userId)
    }
    
    /**
     * Get sync work status
     */
    fun getSyncWorkStatus() = workManager.getWorkInfosForUniqueWorkLiveData(SYNC_WORK_NAME)
    
    /**
     * Get manual sync work status
     */
    fun getManualSyncWorkStatus() = workManager.getWorkInfosForUniqueWorkLiveData(MANUAL_SYNC_WORK_NAME)
    
    /**
     * Update sync frequency for user
     */
    suspend fun updateSyncFrequency(userId: String, newFrequency: SyncFrequency) {
        database.userProfileDao().updateSyncFrequency(userId, newFrequency, System.currentTimeMillis())
        scheduleSyncForUser(userId) // Reschedule with new frequency
    }
    
    /**
     * Enable/disable auto sync
     */
    suspend fun setAutoSyncEnabled(userId: String, enabled: Boolean) {
        database.userProfileDao().updateAutoSyncEnabled(userId, enabled, System.currentTimeMillis())
        if (enabled) {
            scheduleSyncForUser(userId)
        } else {
            cancelPeriodicSync()
        }
    }
    
    /**
     * Update WiFi only preference
     */
    suspend fun setSyncOnlyOnWifi(userId: String, wifiOnly: Boolean) {
        database.userProfileDao().updateSyncOnlyOnWifi(userId, wifiOnly, System.currentTimeMillis())
        scheduleSyncForUser(userId) // Reschedule with new constraints
    }
    
    /**
     * Enable/disable sync notifications
     */
    suspend fun setSyncNotificationsEnabled(userId: String, enabled: Boolean) {
        database.userProfileDao().updateSyncNotificationsEnabled(userId, enabled, System.currentTimeMillis())
    }
}
