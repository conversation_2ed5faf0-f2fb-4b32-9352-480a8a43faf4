package me.fazlerabbie.mindasistant.auth

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.google.firebase.auth.FirebaseUser
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import me.fazlerabbie.mindasistant.firestore.UserProfileService

class AuthViewModel(
    private val authManager: AuthManager = AuthManager(),
    private val userProfileService: UserProfileService = UserProfileService()
) : ViewModel() {
    
    private val _authState = MutableStateFlow<AuthState>(AuthState.Loading)
    val authState: StateFlow<AuthState> = _authState.asStateFlow()
    
    private val _signUpState = MutableStateFlow<SignUpState>(SignUpState.Idle)
    val signUpState: StateFlow<SignUpState> = _signUpState.asStateFlow()
    
    private val _signInState = MutableStateFlow<SignInState>(SignInState.Idle)
    val signInState: StateFlow<SignInState> = _signInState.asStateFlow()
    
    init {
        observeAuthState()
    }
    
    private fun observeAuthState() {
        viewModelScope.launch {
            authManager.authState.collect { user ->
                if (user != null) {
                    // Load user profile from Firestore
                    userProfileService.getUserProfile(user.uid).fold(
                        onSuccess = { userProfile ->
                            _authState.value = AuthState.Authenticated(user, userProfile)
                        },
                        onFailure = {
                            _authState.value = AuthState.Authenticated(user, null)
                        }
                    )
                } else {
                    _authState.value = AuthState.Unauthenticated
                }
            }
        }
    }
    
    fun signUp(
        name: String,
        email: String,
        password: String,
        mobile: String,
        occupation: String,
        bloodGroup: String,
        upazila: String,
        district: String
    ) {
        viewModelScope.launch {
            _signUpState.value = SignUpState.Loading
            
            // Validate inputs
            val validationError = validateSignUpInputs(name, email, password, mobile, occupation, upazila, district)
            if (validationError != null) {
                _signUpState.value = SignUpState.Error(validationError)
                return@launch
            }
            
            when (val result = authManager.signUp(email, password, name)) {
                is AuthResult.Success -> {
                    result.user?.let { user ->
                        // Create user profile
                        val userProfile = UserProfile(
                            uid = user.uid,
                            name = name,
                            email = email,
                            mobile = mobile,
                            occupation = occupation,
                            bloodGroup = bloodGroup.takeIf { it.isNotBlank() },
                            upazila = upazila,
                            district = district
                        )

                        // Save user profile to Firestore
                        userProfileService.saveUserProfile(userProfile).fold(
                            onSuccess = {
                                _signUpState.value = SignUpState.Success(userProfile)
                            },
                            onFailure = { exception ->
                                _signUpState.value = SignUpState.Error("Failed to save user profile: ${exception.message}")
                            }
                        )
                    } ?: run {
                        _signUpState.value = SignUpState.Error("Failed to create user")
                    }
                }
                is AuthResult.Error -> {
                    _signUpState.value = SignUpState.Error(result.message)
                }
            }
        }
    }
    
    fun signIn(email: String, password: String) {
        viewModelScope.launch {
            _signInState.value = SignInState.Loading
            
            // Validate inputs
            if (email.isBlank() || password.isBlank()) {
                _signInState.value = SignInState.Error("Email and password are required")
                return@launch
            }
            
            when (val result = authManager.signIn(email, password)) {
                is AuthResult.Success -> {
                    _signInState.value = SignInState.Success
                }
                is AuthResult.Error -> {
                    _signInState.value = SignInState.Error(result.message)
                }
            }
        }
    }
    
    fun signOut() {
        viewModelScope.launch {
            authManager.signOut()
        }
    }
    
    fun resetPassword(email: String) {
        viewModelScope.launch {
            if (email.isBlank()) {
                _signInState.value = SignInState.Error("Email is required")
                return@launch
            }
            
            when (val result = authManager.sendPasswordResetEmail(email)) {
                is AuthResult.Success -> {
                    _signInState.value = SignInState.PasswordResetSent
                }
                is AuthResult.Error -> {
                    _signInState.value = SignInState.Error(result.message)
                }
            }
        }
    }
    
    fun clearSignUpState() {
        _signUpState.value = SignUpState.Idle
    }
    
    fun clearSignInState() {
        _signInState.value = SignInState.Idle
    }
    
    private fun validateSignUpInputs(
        name: String,
        email: String,
        password: String,
        mobile: String,
        occupation: String,
        upazila: String,
        district: String
    ): String? {
        return when {
            name.isBlank() -> "Name is required"
            email.isBlank() -> "Email is required"
            !android.util.Patterns.EMAIL_ADDRESS.matcher(email).matches() -> "Invalid email format"
            password.isBlank() -> "Password is required"
            password.length < 6 -> "Password must be at least 6 characters"
            mobile.isBlank() -> "Mobile number is required"
            mobile.length < 10 -> "Invalid mobile number"
            occupation.isBlank() -> "Occupation is required"
            upazila.isBlank() -> "Upazila is required"
            district.isBlank() -> "District is required"
            else -> null
        }
    }
}

sealed class AuthState {
    object Loading : AuthState()
    object Unauthenticated : AuthState()
    data class Authenticated(val user: FirebaseUser, val userProfile: UserProfile? = null) : AuthState()
}

sealed class SignUpState {
    object Idle : SignUpState()
    object Loading : SignUpState()
    data class Success(val userProfile: UserProfile) : SignUpState()
    data class Error(val message: String) : SignUpState()
}

sealed class SignInState {
    object Idle : SignInState()
    object Loading : SignInState()
    object Success : SignInState()
    object PasswordResetSent : SignInState()
    data class Error(val message: String) : SignInState()
}
