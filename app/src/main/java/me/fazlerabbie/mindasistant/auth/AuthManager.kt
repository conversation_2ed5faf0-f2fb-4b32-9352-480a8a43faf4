package me.fazlerabbie.mindasistant.auth

import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.auth.FirebaseUser
import com.google.firebase.auth.UserProfileChangeRequest
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.callbackFlow
import kotlinx.coroutines.tasks.await
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class AuthManager @Inject constructor() {
    
    private val auth: FirebaseAuth = FirebaseAuth.getInstance()
    
    val currentUser: FirebaseUser?
        get() = auth.currentUser
    
    val authState: Flow<FirebaseUser?> = callbackFlow {
        val listener = FirebaseAuth.AuthStateListener { auth ->
            trySend(auth.currentUser)
        }
        auth.addAuthStateListener(listener)
        awaitClose { auth.removeAuthStateListener(listener) }
    }
    
    suspend fun signUp(
        email: String,
        password: String,
        name: String
    ): AuthResult {
        return try {
            val result = auth.createUserWithEmailAndPassword(email, password).await()
            val user = result.user
            
            // Update user profile with name
            user?.let {
                val profileUpdates = UserProfileChangeRequest.Builder()
                    .setDisplayName(name)
                    .build()
                it.updateProfile(profileUpdates).await()
            }
            
            AuthResult.Success(user)
        } catch (e: Exception) {
            AuthResult.Error(e.message ?: "Sign up failed")
        }
    }
    
    suspend fun signIn(email: String, password: String): AuthResult {
        return try {
            val result = auth.signInWithEmailAndPassword(email, password).await()
            AuthResult.Success(result.user)
        } catch (e: Exception) {
            AuthResult.Error(e.message ?: "Sign in failed")
        }
    }
    
    suspend fun signOut(): AuthResult {
        return try {
            auth.signOut()
            AuthResult.Success(null)
        } catch (e: Exception) {
            AuthResult.Error(e.message ?: "Sign out failed")
        }
    }
    
    suspend fun sendPasswordResetEmail(email: String): AuthResult {
        return try {
            auth.sendPasswordResetEmail(email).await()
            AuthResult.Success(null)
        } catch (e: Exception) {
            AuthResult.Error(e.message ?: "Failed to send password reset email")
        }
    }
    
    fun isUserSignedIn(): Boolean {
        return currentUser != null
    }
}

sealed class AuthResult {
    data class Success(val user: FirebaseUser?) : AuthResult()
    data class Error(val message: String) : AuthResult()
}

data class UserProfile(
    val uid: String = "",
    val name: String = "",
    val email: String = "",
    val mobile: String = "",
    val occupation: String = "",
    val bloodGroup: String? = null,
    val upazila: String = "",
    val district: String = "",
    val createdAt: Long = System.currentTimeMillis(),
    // Sync preferences
    val syncFrequency: SyncFrequency = SyncFrequency.WEEKLY,
    val autoSyncEnabled: Boolean = true,
    val syncOnlyOnWifi: Boolean = true,
    val lastSyncTime: Long = 0L,
    val syncNotificationsEnabled: Boolean = true
) {
    // No-argument constructor for Firestore
    constructor() : this("", "", "", "", "", null, "", "", System.currentTimeMillis(), SyncFrequency.WEEKLY, true, true, 0L, true)
}

enum class SyncFrequency(val displayName: String, val intervalMillis: Long) {
    DAILY("Daily", 24 * 60 * 60 * 1000L), // 1 day
    WEEKLY("Weekly", 7 * 24 * 60 * 60 * 1000L), // 7 days
    MONTHLY("Monthly", 30 * 24 * 60 * 60 * 1000L) // 30 days
}
