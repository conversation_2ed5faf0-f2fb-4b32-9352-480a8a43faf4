package me.fazlerabbie.mindasistant.ui.screens

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.google.firebase.auth.FirebaseAuth
import me.fazlerabbie.mindasistant.auth.SyncFrequency
import me.fazlerabbie.mindasistant.ui.viewmodel.UserSettingsViewModel

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun UserSettingsScreen(
    onBackClick: () -> Unit,
    viewModel: UserSettingsViewModel = viewModel()
) {
    val context = LocalContext.current
    val currentUser = FirebaseAuth.getInstance().currentUser
    val userId = currentUser?.uid ?: ""
    
    val uiState by viewModel.uiState.collectAsState()
    
    // Load user settings when screen opens
    LaunchedEffect(userId) {
        if (userId.isNotEmpty()) {
            viewModel.loadUserSettings(userId)
        }
    }
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .verticalScroll(rememberScrollState())
    ) {
        // Top App Bar
        TopAppBar(
            title = {
                Text(
                    text = "Settings",
                    fontSize = 20.sp,
                    fontWeight = FontWeight.Bold
                )
            },
            navigationIcon = {
                IconButton(onClick = onBackClick) {
                    Icon(
                        imageVector = Icons.Default.ArrowBack,
                        contentDescription = "Back"
                    )
                }
            }
        )
        
        // Settings Content
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp)
        ) {
            // Sync Settings Section
            SyncSettingsSection(
                syncFrequency = uiState.syncFrequency,
                autoSyncEnabled = uiState.autoSyncEnabled,
                syncOnlyOnWifi = uiState.syncOnlyOnWifi,
                syncNotificationsEnabled = uiState.syncNotificationsEnabled,
                lastSyncTime = uiState.lastSyncTime,
                onSyncFrequencyChanged = { frequency ->
                    viewModel.updateSyncFrequency(userId, frequency)
                },
                onAutoSyncToggled = { enabled ->
                    viewModel.setAutoSyncEnabled(userId, enabled)
                },
                onWifiOnlyToggled = { wifiOnly ->
                    viewModel.setSyncOnlyOnWifi(userId, wifiOnly)
                },
                onNotificationsToggled = { enabled ->
                    viewModel.setSyncNotificationsEnabled(userId, enabled)
                },
                onManualSyncClicked = {
                    viewModel.triggerManualSync(userId)
                }
            )
            
            Spacer(modifier = Modifier.height(24.dp))
            
            // User Profile Section
            UserProfileSection(
                userProfile = uiState.userProfile,
                onProfileUpdateClicked = {
                    // Navigate to profile edit screen
                }
            )
            
            Spacer(modifier = Modifier.height(24.dp))
            
            // App Settings Section
            AppSettingsSection()
        }
    }
}

@Composable
private fun SyncSettingsSection(
    syncFrequency: SyncFrequency,
    autoSyncEnabled: Boolean,
    syncOnlyOnWifi: Boolean,
    syncNotificationsEnabled: Boolean,
    lastSyncTime: Long,
    onSyncFrequencyChanged: (SyncFrequency) -> Unit,
    onAutoSyncToggled: (Boolean) -> Unit,
    onWifiOnlyToggled: (Boolean) -> Unit,
    onNotificationsToggled: (Boolean) -> Unit,
    onManualSyncClicked: () -> Unit
) {
    var showFrequencyDialog by remember { mutableStateOf(false) }
    
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "Sync Settings",
                fontSize = 18.sp,
                fontWeight = FontWeight.Bold,
                modifier = Modifier.padding(bottom = 16.dp)
            )
            
            // Auto Sync Toggle
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 8.dp),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Column(modifier = Modifier.weight(1f)) {
                    Text(
                        text = "Auto Sync",
                        fontSize = 16.sp,
                        fontWeight = FontWeight.Medium
                    )
                    Text(
                        text = "Automatically sync data in background",
                        fontSize = 12.sp,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
                Switch(
                    checked = autoSyncEnabled,
                    onCheckedChange = onAutoSyncToggled
                )
            }
            
            if (autoSyncEnabled) {
                Divider(modifier = Modifier.padding(vertical = 8.dp))
                
                // Sync Frequency
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 8.dp),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Column(modifier = Modifier.weight(1f)) {
                        Text(
                            text = "Sync Frequency",
                            fontSize = 16.sp,
                            fontWeight = FontWeight.Medium
                        )
                        Text(
                            text = "Currently: ${syncFrequency.displayName}",
                            fontSize = 12.sp,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                    TextButton(onClick = { showFrequencyDialog = true }) {
                        Text("Change")
                    }
                }
                
                // WiFi Only Toggle
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 8.dp),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Column(modifier = Modifier.weight(1f)) {
                        Text(
                            text = "WiFi Only",
                            fontSize = 16.sp,
                            fontWeight = FontWeight.Medium
                        )
                        Text(
                            text = "Sync only when connected to WiFi",
                            fontSize = 12.sp,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                    Switch(
                        checked = syncOnlyOnWifi,
                        onCheckedChange = onWifiOnlyToggled
                    )
                }
            }
            
            Divider(modifier = Modifier.padding(vertical = 8.dp))
            
            // Sync Notifications Toggle
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 8.dp),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Column(modifier = Modifier.weight(1f)) {
                    Text(
                        text = "Sync Notifications",
                        fontSize = 16.sp,
                        fontWeight = FontWeight.Medium
                    )
                    Text(
                        text = "Show notifications for sync status",
                        fontSize = 12.sp,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
                Switch(
                    checked = syncNotificationsEnabled,
                    onCheckedChange = onNotificationsToggled
                )
            }
            
            Divider(modifier = Modifier.padding(vertical = 8.dp))
            
            // Last Sync Time
            if (lastSyncTime > 0) {
                Text(
                    text = "Last sync: ${java.text.SimpleDateFormat("MMM dd, yyyy HH:mm", java.util.Locale.getDefault()).format(java.util.Date(lastSyncTime))}",
                    fontSize = 12.sp,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    modifier = Modifier.padding(vertical = 4.dp)
                )
            }
            
            // Manual Sync Button
            Button(
                onClick = onManualSyncClicked,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(top = 8.dp)
            ) {
                Icon(
                    imageVector = Icons.Default.Sync,
                    contentDescription = null,
                    modifier = Modifier.size(18.dp)
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text("Sync Now")
            }
        }
    }
    
    // Sync Frequency Dialog
    if (showFrequencyDialog) {
        SyncFrequencyDialog(
            currentFrequency = syncFrequency,
            onFrequencySelected = { frequency ->
                onSyncFrequencyChanged(frequency)
                showFrequencyDialog = false
            },
            onDismiss = { showFrequencyDialog = false }
        )
    }
}

@Composable
private fun UserProfileSection(
    userProfile: me.fazlerabbie.mindasistant.auth.UserProfile?,
    onProfileUpdateClicked: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "Profile",
                fontSize = 18.sp,
                fontWeight = FontWeight.Bold,
                modifier = Modifier.padding(bottom = 16.dp)
            )

            userProfile?.let { profile ->
                ProfileInfoRow("Name", profile.name)
                ProfileInfoRow("Email", profile.email)
                ProfileInfoRow("Mobile", profile.mobile)
                ProfileInfoRow("Occupation", profile.occupation)
                if (!profile.bloodGroup.isNullOrEmpty()) {
                    ProfileInfoRow("Blood Group", profile.bloodGroup)
                }
                ProfileInfoRow("Location", "${profile.upazila}, ${profile.district}")

                Button(
                    onClick = onProfileUpdateClicked,
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(top = 16.dp)
                ) {
                    Icon(
                        imageVector = Icons.Default.Edit,
                        contentDescription = null,
                        modifier = Modifier.size(18.dp)
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text("Update Profile")
                }
            }
        }
    }
}

@Composable
private fun ProfileInfoRow(label: String, value: String) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 4.dp),
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        Text(
            text = label,
            fontSize = 14.sp,
            color = MaterialTheme.colorScheme.onSurfaceVariant,
            modifier = Modifier.weight(1f)
        )
        Text(
            text = value,
            fontSize = 14.sp,
            fontWeight = FontWeight.Medium,
            modifier = Modifier.weight(2f)
        )
    }
}

@Composable
private fun AppSettingsSection() {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "App Settings",
                fontSize = 18.sp,
                fontWeight = FontWeight.Bold,
                modifier = Modifier.padding(bottom = 16.dp)
            )

            // About App
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 8.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = Icons.Default.Info,
                    contentDescription = null,
                    modifier = Modifier.size(24.dp)
                )
                Spacer(modifier = Modifier.width(16.dp))
                Column(modifier = Modifier.weight(1f)) {
                    Text(
                        text = "About",
                        fontSize = 16.sp,
                        fontWeight = FontWeight.Medium
                    )
                    Text(
                        text = "App version and information",
                        fontSize = 12.sp,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
                IconButton(onClick = { /* Navigate to about screen */ }) {
                    Icon(
                        imageVector = Icons.Default.ChevronRight,
                        contentDescription = "Go to About"
                    )
                }
            }

            // Privacy Policy
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 8.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = Icons.Default.Security,
                    contentDescription = null,
                    modifier = Modifier.size(24.dp)
                )
                Spacer(modifier = Modifier.width(16.dp))
                Column(modifier = Modifier.weight(1f)) {
                    Text(
                        text = "Privacy Policy",
                        fontSize = 16.sp,
                        fontWeight = FontWeight.Medium
                    )
                    Text(
                        text = "How we handle your data",
                        fontSize = 12.sp,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
                IconButton(onClick = { /* Navigate to privacy policy */ }) {
                    Icon(
                        imageVector = Icons.Default.ChevronRight,
                        contentDescription = "Go to Privacy Policy"
                    )
                }
            }
        }
    }
}

@Composable
private fun SyncFrequencyDialog(
    currentFrequency: SyncFrequency,
    onFrequencySelected: (SyncFrequency) -> Unit,
    onDismiss: () -> Unit
) {
    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Text("Select Sync Frequency")
        },
        text = {
            Column {
                SyncFrequency.values().forEach { frequency ->
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(vertical = 4.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        RadioButton(
                            selected = frequency == currentFrequency,
                            onClick = { onFrequencySelected(frequency) }
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Column {
                            Text(
                                text = frequency.displayName,
                                fontSize = 16.sp
                            )
                            Text(
                                text = when (frequency) {
                                    SyncFrequency.DAILY -> "Sync every day"
                                    SyncFrequency.WEEKLY -> "Sync every week"
                                    SyncFrequency.MONTHLY -> "Sync every month"
                                },
                                fontSize = 12.sp,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                    }
                }
            }
        },
        confirmButton = {
            TextButton(onClick = onDismiss) {
                Text("Cancel")
            }
        }
    )
}
