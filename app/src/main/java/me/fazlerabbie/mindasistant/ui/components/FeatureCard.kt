package me.fazlerabbie.mindasistant.ui.components

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp

@Composable
fun FeatureCard(
    title: String,
    icon: ImageVector,
    gradientColors: List<Color>,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier
            .fillMaxWidth()
            .height(80.dp)
            .clickable { onClick() },
        shape = RoundedCornerShape(12.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp),
        colors = CardDefaults.cardColors(containerColor = Color.Transparent)
    ) {
        Box(
            modifier = Modifier
                .fillMaxSize()
                .background(
                    brush = Brush.linearGradient(gradientColors),
                    shape = RoundedCornerShape(12.dp)
                )
                .padding(horizontal = 20.dp, vertical = 16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxSize(),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Text(
                    text = title,
                    color = Color.White,
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Bold,
                    modifier = Modifier.weight(1f)
                )
                Icon(
                    imageVector = icon,
                    contentDescription = title,
                    modifier = Modifier.size(32.dp),
                    tint = Color.White
                )
            }
        }
    }
}

// Feature data class
data class Feature(
    val title: String,
    val icon: ImageVector,
    val gradientColors: List<Color>,
    val route: String
)

// Predefined feature list
object FeatureList {
    val features = listOf(
        Feature(
            title = "Due Remainder",
            icon = Icons.Default.Notifications,
            gradientColors = listOf(Color(0xFFFF6B6B), Color(0xFFFF8E8E)),
            route = "due_remainder"
        ),
        Feature(
            title = "Todo List",
            icon = Icons.Default.CheckCircle,
            gradientColors = listOf(Color(0xFF4ECDC4), Color(0xFF44A08D)),
            route = "todo_list"
        ),
        Feature(
            title = "Calculator",
            icon = Icons.Default.Calculate,
            gradientColors = listOf(Color(0xFF667eea), Color(0xFF764ba2)),
            route = "calculator"
        ),
        Feature(
            title = "Notepad",
            icon = Icons.Default.Edit,
            gradientColors = listOf(Color(0xFFf093fb), Color(0xFFf5576c)),
            route = "notepad"
        ),
        Feature(
            title = "Tips and Tricks",
            icon = Icons.Default.Lightbulb,
            gradientColors = listOf(Color(0xFF059669), Color(0xFF047857)), // Modern emerald green
            route = "tips_tricks"
        ),
        Feature(
            title = "Medicine Index",
            icon = Icons.Default.LocalPharmacy,
            gradientColors = listOf(Color(0xFF7C3AED), Color(0xFF6D28D9)), // Modern purple
            route = "medicine_index"
        ),
        Feature(
            title = "Feature Request",
            icon = Icons.Default.Add,
            gradientColors = listOf(Color(0xFFDC2626), Color(0xFFB91C1C)), // Modern red
            route = "feature_request"
        ),
        Feature(
            title = "Life Moment",
            icon = Icons.Default.PhotoCamera,
            gradientColors = listOf(Color(0xFFDB2777), Color(0xFFBE185D)), // Modern pink
            route = "life_moment"
        )
    )
}
