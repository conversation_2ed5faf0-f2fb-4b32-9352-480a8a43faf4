package me.fazlerabbie.mindasistant.ui.screens.features.todolist

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.viewModelScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import me.fazlerabbie.mindasistant.data.model.*
import me.fazlerabbie.mindasistant.data.repository.TodoRepository

data class TodoUiState(
    val isLoading: Boolean = false,
    val error: String? = null,
    val message: String? = null
)

class TodoViewModel(
    application: Application
) : AndroidViewModel(application) {
    private val todoRepository = TodoRepository(application)
    
    private val _uiState = MutableStateFlow(TodoUiState())
    val uiState: StateFlow<TodoUiState> = _uiState.asStateFlow()

    private val _allTodos = MutableStateFlow<List<TodoItem>>(emptyList())
    private val _selectedFilter = MutableStateFlow(TodoFilter.ALL)
    private val _sortBy = MutableStateFlow(TodoSortBy.CREATED_DATE)
    private val _sortOrder = MutableStateFlow(SortOrder.DESCENDING)
    private val _searchQuery = MutableStateFlow("")
    private val _currentUserId = MutableStateFlow<String?>(null)
    
    val selectedFilter: StateFlow<TodoFilter> = _selectedFilter.asStateFlow()
    val sortBy: StateFlow<TodoSortBy> = _sortBy.asStateFlow()
    val sortOrder: StateFlow<SortOrder> = _sortOrder.asStateFlow()
    val searchQuery: StateFlow<String> = _searchQuery.asStateFlow()
    
    // Filtered and sorted todos
    val filteredTodos: StateFlow<List<TodoItem>> = combine(
        _allTodos,
        _selectedFilter,
        _sortBy,
        _sortOrder,
        _searchQuery
    ) { todos, filter, sortBy, sortOrder, query ->
        var filteredList = when (filter) {
            TodoFilter.ALL -> todos
            TodoFilter.PENDING -> todos.filter { !it.isCompleted }
            TodoFilter.COMPLETED -> todos.filter { it.isCompleted }
            TodoFilter.TODAY -> todos.filter { it.isDueToday() && !it.isCompleted }
            TodoFilter.TOMORROW -> todos.filter { it.isDueTomorrow() && !it.isCompleted }
            TodoFilter.OVERDUE -> todos.filter { it.isOverdue() }
            TodoFilter.UPCOMING -> todos.filter { it.isUpcoming() }
            TodoFilter.HIGH_PRIORITY -> todos.filter { it.priority == TodoPriority.HIGH || it.priority == TodoPriority.URGENT }
            TodoFilter.MEDIUM_PRIORITY -> todos.filter { it.priority == TodoPriority.MEDIUM }
            TodoFilter.LOW_PRIORITY -> todos.filter { it.priority == TodoPriority.LOW }
        }
        
        // Apply search filter
        if (query.isNotBlank()) {
            filteredList = filteredList.filter { todo ->
                todo.title.contains(query, ignoreCase = true) ||
                todo.description.contains(query, ignoreCase = true) ||
                todo.tags.any { tag -> tag.contains(query, ignoreCase = true) }
            }
        }
        
        // Apply sorting
        when (sortBy) {
            TodoSortBy.CREATED_DATE -> {
                if (sortOrder == SortOrder.ASCENDING) {
                    filteredList.sortedBy { it.createdAt }
                } else {
                    filteredList.sortedByDescending { it.createdAt }
                }
            }
            TodoSortBy.DUE_DATE -> {
                if (sortOrder == SortOrder.ASCENDING) {
                    filteredList.sortedBy { it.dueDate ?: Long.MAX_VALUE }
                } else {
                    filteredList.sortedByDescending { it.dueDate ?: 0L }
                }
            }
            TodoSortBy.PRIORITY -> {
                if (sortOrder == SortOrder.ASCENDING) {
                    filteredList.sortedBy { it.priority.ordinal }
                } else {
                    filteredList.sortedByDescending { it.priority.ordinal }
                }
            }
            TodoSortBy.TITLE -> {
                if (sortOrder == SortOrder.ASCENDING) {
                    filteredList.sortedBy { it.title.lowercase() }
                } else {
                    filteredList.sortedByDescending { it.title.lowercase() }
                }
            }
            TodoSortBy.CATEGORY -> {
                if (sortOrder == SortOrder.ASCENDING) {
                    filteredList.sortedBy { it.category.ordinal }
                } else {
                    filteredList.sortedByDescending { it.category.ordinal }
                }
            }
        }
    }.stateIn(
        scope = viewModelScope,
        started = SharingStarted.WhileSubscribed(5000),
        initialValue = emptyList()
    )
    
    // Statistics
    val todoStats: StateFlow<TodoStats> = _allTodos.map { todos ->
        TodoStats(
            total = todos.size,
            completed = todos.count { it.isCompleted },
            pending = todos.count { !it.isCompleted },
            overdue = todos.count { it.isOverdue() },
            dueToday = todos.count { it.isDueToday() && !it.isCompleted },
            dueTomorrow = todos.count { it.isDueTomorrow() && !it.isCompleted }
        )
    }.stateIn(
        scope = viewModelScope,
        started = SharingStarted.WhileSubscribed(5000),
        initialValue = TodoStats()
    )
    
    fun loadTodos(userId: String) {
        // Only set up the listener if we don't have one for this user already
        if (_currentUserId.value != userId) {
            _currentUserId.value = userId
            _uiState.value = _uiState.value.copy(isLoading = true)

            viewModelScope.launch {
                todoRepository.getTodosFlow(userId)
                    .catch { exception ->
                        _uiState.value = _uiState.value.copy(
                            isLoading = false,
                            error = exception.message
                        )
                    }
                    .collect { todos ->
                        _allTodos.value = todos
                        _uiState.value = _uiState.value.copy(
                            isLoading = false,
                            error = null
                        )
                    }
            }
        }
    }

    fun refreshTodos() {
        _currentUserId.value?.let { userId ->
            println("TodoViewModel: Refreshing todos for user: $userId")
            _uiState.value = _uiState.value.copy(isLoading = true)
            viewModelScope.launch {
                try {
                    val todos = todoRepository.getTodos(userId)
                    println("TodoViewModel: Refreshed ${todos.size} todos")
                    _allTodos.value = todos
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        error = null
                    )
                } catch (exception: Exception) {
                    println("TodoViewModel: Error refreshing todos - ${exception.message}")
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        error = exception.message
                    )
                }
            }
        } ?: run {
            println("TodoViewModel: No current user ID for refresh")
        }
    }
    
    fun saveTodoItem(todoItem: TodoItem) {
        viewModelScope.launch {
            todoRepository.saveTodo(todoItem).fold(
                onSuccess = {
                    _uiState.value = _uiState.value.copy(
                        message = if (todoItem.id.isEmpty()) "Todo created successfully" else "Todo updated successfully"
                    )
                    // Add a small delay to ensure local database write is complete, then refresh
                    viewModelScope.launch {
                        delay(500) // 500ms delay
                        refreshTodos()
                    }
                },
                onFailure = { exception ->
                    _uiState.value = _uiState.value.copy(
                        error = exception.message
                    )
                }
            )
        }
    }
    
    fun deleteTodoItem(todoId: String) {
        viewModelScope.launch {
            todoRepository.deleteTodo(todoId).fold(
                onSuccess = {
                    _uiState.value = _uiState.value.copy(
                        message = "Todo deleted successfully"
                    )
                },
                onFailure = { exception ->
                    _uiState.value = _uiState.value.copy(
                        error = exception.message
                    )
                }
            )
        }
    }
    
    fun toggleTodoCompletion(todoId: String, isCompleted: Boolean) {
        viewModelScope.launch {
            todoRepository.toggleTodoCompletion(todoId, isCompleted).fold(
                onSuccess = {
                    _uiState.value = _uiState.value.copy(
                        message = if (isCompleted) "Todo marked as completed" else "Todo marked as pending"
                    )
                },
                onFailure = { exception ->
                    _uiState.value = _uiState.value.copy(
                        error = exception.message
                    )
                }
            )
        }
    }
    
    fun setFilter(filter: TodoFilter) {
        _selectedFilter.value = filter
    }
    
    fun setSorting(sortBy: TodoSortBy, sortOrder: SortOrder) {
        _sortBy.value = sortBy
        _sortOrder.value = sortOrder
    }
    
    fun setSearchQuery(query: String) {
        _searchQuery.value = query
    }
    
    fun deleteCompletedTodos(userId: String) {
        viewModelScope.launch {
            todoRepository.deleteCompletedTodos(userId).fold(
                onSuccess = { count ->
                    _uiState.value = _uiState.value.copy(
                        message = "$count completed todos deleted"
                    )
                },
                onFailure = { exception ->
                    _uiState.value = _uiState.value.copy(
                        error = exception.message
                    )
                }
            )
        }
    }
    
    fun clearMessage() {
        _uiState.value = _uiState.value.copy(message = null)
    }
    
    fun clearError() {
        _uiState.value = _uiState.value.copy(error = null)
    }
}

data class TodoStats(
    val total: Int = 0,
    val completed: Int = 0,
    val pending: Int = 0,
    val overdue: Int = 0,
    val dueToday: Int = 0,
    val dueTomorrow: Int = 0
)
