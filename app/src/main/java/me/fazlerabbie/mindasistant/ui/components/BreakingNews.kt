package me.fazlerabbie.mindasistant.ui.components

import androidx.compose.foundation.background
import androidx.compose.foundation.basicMarquee
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.BasicText
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp

@Composable
fun BreakingNewsSection(
    modifier: Modifier = Modifier
) {
    // Extended news text to ensure proper scrolling
    val newsText = "আজ বাংলাদেশ সরকার সিদ্ধান্ত নিয়েছে যে, সময় কিভাবে সঠিকভাবে ব্যবহার করা যায়   •   Mind Assistant app is now available for download   •   New features coming soon to Mind Assistant   •   Stay tuned for more updates on Mind Assistant   •   আজ বাংলাদেশ সরকার সিদ্ধান্ত নিয়েছে যে, সময় কিভাবে সঠিকভাবে ব্যবহার করা যায়   •   Mind Assistant app is now available for download   •   "

    // Modern card design
    Card(
        modifier = modifier,
        elevation = CardDefaults.cardElevation(defaultElevation = 1.dp),
        shape = RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface
        )
    ) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // Breaking News Label with modern design
            Box(
                modifier = Modifier
                    .width(90.dp)
                    .height(44.dp)
                    .background(
                        Color(0xFFDC2626), // Modern red
                        RoundedCornerShape(topStart = 12.dp, bottomStart = 12.dp)
                    ),
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = "LIVE",
                    color = Color.White,
                    fontSize = 11.sp,
                    fontWeight = FontWeight.Bold
                )
            }

            // Scrolling News Content with better design
            Box(
                modifier = Modifier
                    .weight(1f)
                    .height(44.dp)
                    .background(
                        MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.3f),
                        RoundedCornerShape(topEnd = 12.dp, bottomEnd = 12.dp)
                    ),
                contentAlignment = Alignment.CenterStart
            ) {
                Text(
                    text = newsText,
                    style = TextStyle(
                        color = MaterialTheme.colorScheme.onSurface,
                        fontSize = 13.sp,
                        fontWeight = FontWeight.Medium
                    ),
                    maxLines = 1,
                    overflow = TextOverflow.Visible,
                    modifier = Modifier
                        .fillMaxWidth()
                        .basicMarquee()
                        .padding(horizontal = 14.dp)
                )
            }
        }
    }
}


