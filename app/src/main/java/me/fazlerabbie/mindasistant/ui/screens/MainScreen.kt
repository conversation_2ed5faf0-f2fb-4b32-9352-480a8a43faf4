package me.fazlerabbie.mindasistant.ui.screens

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.navigation.NavHostController
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.currentBackStackEntryAsState
import androidx.navigation.compose.rememberNavController
import me.fazlerabbie.mindasistant.auth.AuthState
import me.fazlerabbie.mindasistant.auth.AuthViewModel
import me.fazlerabbie.mindasistant.ui.components.BottomNavigationBar
import me.fazlerabbie.mindasistant.ui.screens.features.*

@Composable
fun MainScreen(
    authViewModel: AuthViewModel = viewModel(),
    onNavigateToFeature: (String) -> Unit
) {
    val navController = rememberNavController()
    val navBackStackEntry by navController.currentBackStackEntryAsState()
    val currentRoute = navBackStackEntry?.destination?.route
    val authState by authViewModel.authState.collectAsState()
    val currentAuthState = authState

    val userProfile = if (currentAuthState is AuthState.Authenticated) {
        currentAuthState.userProfile
    } else null

    Scaffold(
        bottomBar = {
            BottomNavigationBar(
                currentRoute = currentRoute,
                onNavigate = { route ->
                    navController.navigate(route) {
                        // Pop up to the start destination to avoid building up a large stack
                        popUpTo(navController.graph.startDestinationId) {
                            saveState = true
                        }
                        // Avoid multiple copies of the same destination when reselecting the same item
                        launchSingleTop = true
                        // Restore state when reselecting a previously selected item
                        restoreState = true
                    }
                }
            )
        }
    ) { innerPadding ->
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(innerPadding)
        ) {
            NavHost(
                navController = navController,
                startDestination = "home"
            ) {
                composable("home") {
                    HomeScreen(
                        userProfile = userProfile,
                        authViewModel = authViewModel,
                        onNavigateToFeature = onNavigateToFeature
                    )
                }
                
                composable("todo_list") {
                    TodoListScreen(
                        onBackClick = { navController.navigate("home") }
                    )
                }
                
                composable("calculator") {
                    CalculatorScreen(
                        onBackClick = { navController.navigate("home") }
                    )
                }
                
                composable("due_remainder") {
                    DueRemainderScreen(
                        onBackClick = { navController.navigate("home") }
                    )
                }
                
                composable("profile") {
                    ProfileScreen(
                        userProfile = userProfile,
                        authViewModel = authViewModel,
                        onNavigateToFeature = onNavigateToFeature,
                        onBackClick = { navController.navigate("home") }
                    )
                }
            }
        }
    }
}
