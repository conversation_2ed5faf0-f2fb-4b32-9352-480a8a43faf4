package me.fazlerabbie.mindasistant.ui.screens.features.todolist

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.lifecycle.viewmodel.compose.viewModel
import com.google.firebase.auth.FirebaseAuth
import me.fazlerabbie.mindasistant.data.model.*
import me.fazlerabbie.mindasistant.ui.screens.features.todolist.components.*

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun TodoListMainScreen(
    onBackClick: () -> Unit,
    viewModel: TodoViewModel = viewModel()
) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    val filteredTodos by viewModel.filteredTodos.collectAsStateWithLifecycle()
    val selectedFilter by viewModel.selectedFilter.collectAsStateWithLifecycle()
    val todoStats by viewModel.todoStats.collectAsStateWithLifecycle()
    val searchQuery by viewModel.searchQuery.collectAsStateWithLifecycle()
    
    var showAddTodoScreen by remember { mutableStateOf(false) }
    var showEditTodoScreen by remember { mutableStateOf(false) }
    var selectedTodo by remember { mutableStateOf<TodoItem?>(null) }
    var showDeleteConfirmation by remember { mutableStateOf(false) }
    var todoToDelete by remember { mutableStateOf<String?>(null) }
    var showSortMenu by remember { mutableStateOf(false) }
    var showSearchBar by remember { mutableStateOf(false) }
    
    val currentUser = FirebaseAuth.getInstance().currentUser
    
    // Load todos when screen opens or when user changes
    LaunchedEffect(currentUser?.uid, Unit) {
        currentUser?.uid?.let { userId ->
            viewModel.loadTodos(userId)
        }
    }

    // Refresh when returning from add/edit screens
    LaunchedEffect(showAddTodoScreen, showEditTodoScreen) {
        if (!showAddTodoScreen && !showEditTodoScreen) {
            viewModel.refreshTodos()
        }
    }

    // Refresh when screen becomes visible
    DisposableEffect(Unit) {
        viewModel.refreshTodos()
        onDispose { }
    }
    
    // Handle messages and errors
    LaunchedEffect(uiState.message) {
        uiState.message?.let {
            // Show snackbar or toast
            viewModel.clearMessage()
        }
    }
    
    LaunchedEffect(uiState.error) {
        uiState.error?.let {
            // Show error snackbar or toast
            viewModel.clearError()
        }
    }
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(
                brush = Brush.verticalGradient(
                    colors = listOf(
                        Color(0xFFFAFAFA),
                        Color(0xFFF5F5F5)
                    )
                )
            )
    ) {
        // Top App Bar
        TopAppBar(
            title = {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        imageVector = Icons.Default.CheckCircle,
                        contentDescription = "Todo List",
                        modifier = Modifier.size(24.dp),
                        tint = MaterialTheme.colorScheme.primary
                    )
                    Spacer(modifier = Modifier.width(12.dp))
                    Text(
                        text = "Todo List",
                        fontSize = 20.sp,
                        fontWeight = FontWeight.Bold
                    )
                }
            },
            navigationIcon = {
                IconButton(onClick = onBackClick) {
                    Icon(
                        imageVector = Icons.Default.ArrowBack,
                        contentDescription = "Back"
                    )
                }
            },
            actions = {
                // Add todo button - main action
                IconButton(onClick = { showAddTodoScreen = true }) {
                    Icon(
                        imageVector = Icons.Default.Add,
                        contentDescription = "Add Todo",
                        tint = MaterialTheme.colorScheme.primary
                    )
                }
            },
            colors = TopAppBarDefaults.topAppBarColors(
                containerColor = Color.Transparent
            )
        )
        
        // Simple filter section - only show essential filters
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp, vertical = 8.dp),
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            // All todos
            FilterChip(
                selected = selectedFilter == TodoFilter.ALL,
                onClick = { viewModel.setFilter(TodoFilter.ALL) },
                label = { Text("All (${todoStats.total})", fontSize = 12.sp) },
                modifier = Modifier.height(36.dp)
            )

            // Pending todos
            FilterChip(
                selected = selectedFilter == TodoFilter.PENDING,
                onClick = { viewModel.setFilter(TodoFilter.PENDING) },
                label = { Text("Pending (${todoStats.pending})", fontSize = 12.sp) },
                modifier = Modifier.height(36.dp)
            )

            // Completed todos
            FilterChip(
                selected = selectedFilter == TodoFilter.COMPLETED,
                onClick = { viewModel.setFilter(TodoFilter.COMPLETED) },
                label = { Text("Done (${todoStats.completed})", fontSize = 12.sp) },
                modifier = Modifier.height(36.dp)
            )
        }
        

        
        // Content
        Box(
            modifier = Modifier
                .fillMaxSize()
                .weight(1f)
        ) {
            if (uiState.isLoading) {
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    CircularProgressIndicator()
                }
            } else if (filteredTodos.isEmpty()) {
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    Card(
                        modifier = Modifier.padding(32.dp),
                        shape = RoundedCornerShape(20.dp),
                        colors = CardDefaults.cardColors(
                            containerColor = MaterialTheme.colorScheme.surface
                        ),
                        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
                    ) {
                        Column(
                            modifier = Modifier.padding(32.dp),
                            horizontalAlignment = Alignment.CenterHorizontally
                        ) {
                            Icon(
                                imageVector = Icons.Default.CheckCircle,
                                contentDescription = "No Todos",
                                modifier = Modifier.size(72.dp),
                                tint = MaterialTheme.colorScheme.primary.copy(alpha = 0.6f)
                            )
                            Spacer(modifier = Modifier.height(20.dp))
                            Text(
                                text = if (searchQuery.isNotEmpty()) "No todos found" else "Ready to get organized?",
                                fontSize = 18.sp,
                                fontWeight = FontWeight.SemiBold,
                                color = MaterialTheme.colorScheme.onSurface
                            )
                            if (searchQuery.isEmpty()) {
                                Spacer(modifier = Modifier.height(8.dp))
                                Text(
                                    text = "Create your first todo to get started",
                                    fontSize = 14.sp,
                                    color = MaterialTheme.colorScheme.onSurfaceVariant
                                )
                            }
                        }
                    }
                }
            } else {
                LazyColumn(
                    modifier = Modifier.fillMaxSize(),
                    contentPadding = PaddingValues(bottom = 16.dp)
                ) {
                    items(filteredTodos, key = { it.id }) { todo ->
                        TodoItemCard(
                            todoItem = todo,
                            onToggleCompletion = { todoId, isCompleted ->
                                viewModel.toggleTodoCompletion(todoId, isCompleted)
                            },
                            onEditTodo = { todoItem ->
                                selectedTodo = todoItem
                                showEditTodoScreen = true
                            },
                            onDeleteTodo = { todoId ->
                                todoToDelete = todoId
                                showDeleteConfirmation = true
                            }
                        )
                    }
                }
            }
        }
    }
    
    // Full screen overlays
    if (showAddTodoScreen) {
        currentUser?.uid?.let { userId ->
            AddEditTodoScreen(
                userId = userId,
                onDismiss = { showAddTodoScreen = false },
                onSave = { todoItem ->
                    viewModel.saveTodoItem(todoItem)
                    showAddTodoScreen = false
                    // Force refresh after saving
                    viewModel.refreshTodos()
                }
            )
        }
    }

    if (showEditTodoScreen && selectedTodo != null) {
        currentUser?.uid?.let { userId ->
            AddEditTodoScreen(
                todoItem = selectedTodo,
                userId = userId,
                onDismiss = {
                    showEditTodoScreen = false
                    selectedTodo = null
                },
                onSave = { todoItem ->
                    viewModel.saveTodoItem(todoItem)
                    showEditTodoScreen = false
                    selectedTodo = null
                    // Force refresh after saving
                    viewModel.refreshTodos()
                }
            )
        }
    }
    
    if (showDeleteConfirmation && todoToDelete != null) {
        AlertDialog(
            onDismissRequest = {
                showDeleteConfirmation = false
                todoToDelete = null
            },
            title = { Text("Delete Todo") },
            text = { Text("Are you sure you want to delete this todo? This action cannot be undone.") },
            confirmButton = {
                TextButton(
                    onClick = {
                        todoToDelete?.let { viewModel.deleteTodoItem(it) }
                        showDeleteConfirmation = false
                        todoToDelete = null
                    }
                ) {
                    Text("Delete", color = Color(0xFFDC2626))
                }
            },
            dismissButton = {
                TextButton(
                    onClick = {
                        showDeleteConfirmation = false
                        todoToDelete = null
                    }
                ) {
                    Text("Cancel")
                }
            }
        )
    }
}
