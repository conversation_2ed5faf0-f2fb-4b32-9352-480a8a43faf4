package me.fazlerabbie.mindasistant.ui.screens.features.calculator

import androidx.lifecycle.ViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import java.text.DecimalFormat
import kotlin.math.pow
import kotlin.math.sqrt

data class CalculatorState(
    val display: String = "0",
    val previousValue: Double = 0.0,
    val operation: Operation? = null,
    val waitingForOperand: Boolean = false,
    val history: List<String> = emptyList(),
    val error: String? = null,
    val currentExpression: String = "",
    val activeOperation: Operation? = null // For button highlighting
)

enum class Operation(val symbol: String) {
    ADD("+"),
    SUBTRACT("-"),
    MULTIPLY("×"),
    DIVIDE("÷"),
    POWER("^"),
    PERCENTAGE("%")
}

class CalculatorViewModel : ViewModel() {
    
    private val _state = MutableStateFlow(CalculatorState())
    val state: StateFlow<CalculatorState> = _state.asStateFlow()
    
    private val decimalFormat = DecimalFormat("#.##########")
    
    fun onNumberClick(number: String) {
        val currentState = _state.value

        if (currentState.waitingForOperand) {
            // Starting a new number after an operation
            val newExpression = currentState.currentExpression + number
            _state.value = currentState.copy(
                display = number,
                waitingForOperand = false,
                error = null,
                currentExpression = newExpression,
                activeOperation = null // Clear highlighting when typing numbers
            )
        } else {
            // Continuing to build the current number
            val newDisplay = if (currentState.display == "0") {
                number
            } else {
                currentState.display + number
            }

            // Update the expression by replacing the last number
            val newExpression = if (currentState.currentExpression.isEmpty()) {
                newDisplay
            } else {
                // Find the last number in the expression and replace it
                val operatorPattern = Regex("[+\\-×÷]")
                val lastOperatorIndex = currentState.currentExpression.indexOfLast {
                    it.toString().matches(operatorPattern)
                }

                if (lastOperatorIndex >= 0) {
                    currentState.currentExpression.substring(0, lastOperatorIndex + 1) + newDisplay
                } else {
                    newDisplay
                }
            }

            _state.value = currentState.copy(
                display = newDisplay,
                error = null,
                currentExpression = newExpression
            )
        }
    }
    
    fun onDecimalClick() {
        val currentState = _state.value

        if (currentState.waitingForOperand) {
            // Starting a new decimal number after an operation
            val newExpression = currentState.currentExpression + "0."
            _state.value = currentState.copy(
                display = "0.",
                waitingForOperand = false,
                error = null,
                currentExpression = newExpression,
                activeOperation = null
            )
        } else if (!currentState.display.contains(".")) {
            // Adding decimal to current number
            val newDisplay = currentState.display + "."

            // Update the expression by replacing the last number
            val newExpression = if (currentState.currentExpression.isEmpty()) {
                newDisplay
            } else {
                val operatorPattern = Regex("[+\\-×÷]")
                val lastOperatorIndex = currentState.currentExpression.indexOfLast {
                    it.toString().matches(operatorPattern)
                }

                if (lastOperatorIndex >= 0) {
                    currentState.currentExpression.substring(0, lastOperatorIndex + 1) + newDisplay
                } else {
                    newDisplay
                }
            }

            _state.value = currentState.copy(
                display = newDisplay,
                error = null,
                currentExpression = newExpression
            )
        }
    }
    
    fun onOperationClick(operation: Operation) {
        val currentState = _state.value
        val inputValue = currentState.display.toDoubleOrNull() ?: return

        // Calculate any percentages in the current expression before adding new operation
        val expressionWithCalculatedPercentages = calculatePercentageInExpression(currentState.currentExpression)

        // Add the operation to the expression
        val newExpression = if (expressionWithCalculatedPercentages.isEmpty()) {
            "${currentState.display}${operation.symbol}"
        } else {
            "${expressionWithCalculatedPercentages}${operation.symbol}"
        }

        _state.value = currentState.copy(
            previousValue = inputValue,
            operation = operation,
            waitingForOperand = true,
            error = null,
            currentExpression = newExpression,
            activeOperation = operation // Highlight the operation button
        )
    }
    
    fun onEqualsClick() {
        val currentState = _state.value

        if (currentState.currentExpression.isEmpty()) {
            return
        }

        // First calculate any percentages in the expression
        val expressionWithCalculatedPercentages = calculatePercentageInExpression(currentState.currentExpression)

        val result = evaluateExpression(expressionWithCalculatedPercentages)

        if (result.isSuccess) {
            val resultValue = result.getOrNull()!!
            val calculation = "${currentState.currentExpression} = ${formatNumber(resultValue)}"

            _state.value = currentState.copy(
                display = formatNumber(resultValue),
                operation = null,
                waitingForOperand = true,
                history = currentState.history + calculation,
                error = null,
                currentExpression = "",
                activeOperation = null // Clear highlighting
            )
        } else {
            _state.value = currentState.copy(
                error = result.exceptionOrNull()?.message ?: "Error",
                operation = null,
                waitingForOperand = true,
                activeOperation = null
            )
        }
    }
    
    fun onClearClick() {
        _state.value = CalculatorState(
            history = _state.value.history
        )
    }

    fun onAllClearClick() {
        _state.value = CalculatorState()
    }
    

    
    fun onSquareRootClick() {
        val currentState = _state.value
        val inputValue = currentState.display.toDoubleOrNull() ?: return
        
        if (inputValue < 0) {
            _state.value = currentState.copy(
                error = "Cannot calculate square root of negative number"
            )
            return
        }
        
        val result = sqrt(inputValue)
        val calculation = "√${formatNumber(inputValue)} = ${formatNumber(result)}"
        
        _state.value = currentState.copy(
            display = formatNumber(result),
            waitingForOperand = true,
            history = currentState.history + calculation,
            error = null
        )
    }
    
    fun onPercentageClick() {
        val currentState = _state.value
        val inputValue = currentState.display.toDoubleOrNull() ?: return

        // Show the % symbol in the expression first
        val expressionWithPercent = if (currentState.currentExpression.isEmpty()) {
            "${currentState.display}%"
        } else {
            "${currentState.currentExpression}%"
        }

        // Update state to show the % symbol in the expression
        _state.value = currentState.copy(
            currentExpression = expressionWithPercent,
            waitingForOperand = true,
            error = null
        )
    }

    // Helper function to calculate percentage when needed (called during equals or operation)
    private fun calculatePercentageInExpression(expression: String): String {
        if (!expression.contains("%")) return expression

        // Find all percentage occurrences and replace them with calculated values
        var result = expression
        val percentPattern = Regex("(\\d+(?:\\.\\d+)?)%")

        percentPattern.findAll(expression).forEach { match ->
            val percentValue = match.groupValues[1].toDoubleOrNull() ?: return@forEach
            val fullMatch = match.value

            // Find the base number for contextual percentage calculation
            val beforePercent = expression.substring(0, match.range.first)
            val operatorPattern = Regex("[+\\-×÷]")
            val operators = operatorPattern.findAll(beforePercent).toList()

            val calculatedValue = if (operators.isNotEmpty()) {
                // Get the last operator and find the number before it
                val lastOperator = operators.last()
                val beforeOperator = beforePercent.substring(0, lastOperator.range.first)
                val previousNumbers = beforeOperator.split(operatorPattern).filter { it.isNotEmpty() }

                if (previousNumbers.isNotEmpty()) {
                    val baseNumber = previousNumbers.last().toDoubleOrNull() ?: 100.0
                    (baseNumber * percentValue) / 100
                } else {
                    percentValue / 100
                }
            } else {
                percentValue / 100
            }

            result = result.replace(fullMatch, formatNumber(calculatedValue))
        }

        return result
    }
    
    fun onPlusMinusClick() {
        val currentState = _state.value
        val inputValue = currentState.display.toDoubleOrNull() ?: return

        val result = -inputValue

        // Update the expression by replacing the last number with the negated result
        val newExpression = if (currentState.currentExpression.isEmpty()) {
            formatNumber(result)
        } else {
            val operatorPattern = Regex("[+\\-×÷]")
            val lastOperatorIndex = currentState.currentExpression.indexOfLast {
                it.toString().matches(operatorPattern)
            }

            if (lastOperatorIndex >= 0) {
                currentState.currentExpression.substring(0, lastOperatorIndex + 1) + formatNumber(result)
            } else {
                formatNumber(result)
            }
        }

        _state.value = currentState.copy(
            display = formatNumber(result),
            currentExpression = newExpression,
            error = null
        )
    }
    

    
    fun clearHistory() {
        _state.value = _state.value.copy(history = emptyList())
    }

    // Add delete/backspace functionality
    fun onDeleteClick() {
        val currentState = _state.value

        if (currentState.currentExpression.isEmpty()) {
            return
        }

        // Remove the last character from the expression
        val newExpression = currentState.currentExpression.dropLast(1)

        // Update the display to show the current number being typed
        val newDisplay = if (newExpression.isEmpty()) {
            "0"
        } else {
            // Find the last number in the expression
            val operatorPattern = Regex("[+\\-×÷]")
            val lastOperatorIndex = newExpression.indexOfLast {
                it.toString().matches(operatorPattern)
            }

            if (lastOperatorIndex >= 0) {
                val lastNumber = newExpression.substring(lastOperatorIndex + 1)
                if (lastNumber.isEmpty()) "0" else lastNumber
            } else {
                newExpression.ifEmpty { "0" }
            }
        }

        // Check if we just deleted an operator (now waiting for operand)
        val isWaitingForOperand = newExpression.isNotEmpty() &&
            newExpression.last().toString().matches(Regex("[+\\-×÷]"))

        _state.value = currentState.copy(
            display = newDisplay,
            error = null,
            currentExpression = newExpression,
            waitingForOperand = isWaitingForOperand,
            activeOperation = if (isWaitingForOperand) {
                Operation.values().find { it.symbol == newExpression.last().toString() }
            } else null
        )
    }

    // Evaluate mathematical expression with proper precedence
    private fun evaluateExpression(expression: String): Result<Double> {
        return try {
            if (expression.isEmpty()) {
                return Result.success(0.0)
            }

            // Simple expression evaluator with precedence
            val result = evaluateWithPrecedence(expression)

            if (result.isInfinite() || result.isNaN()) {
                throw ArithmeticException("Invalid calculation result")
            }

            Result.success(result)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    private fun evaluateWithPrecedence(expression: String): Double {
        // Split by + and - (lower precedence)
        val addSubParts = expression.split(Regex("(?=[+\\-])|(?<=[+\\-])")).filter { it.isNotEmpty() }
        var result = 0.0
        var currentOp = "+"

        for (part in addSubParts) {
            when (part) {
                "+" -> currentOp = "+"
                "-" -> currentOp = "-"
                else -> {
                    val value = evaluateMultiplyDivide(part)
                    result = when (currentOp) {
                        "+" -> result + value
                        "-" -> result - value
                        else -> value
                    }
                }
            }
        }

        return result
    }

    private fun evaluateMultiplyDivide(expression: String): Double {
        val mulDivParts = expression.split(Regex("(?=[×÷])|(?<=[×÷])")).filter { it.isNotEmpty() }
        var result = 0.0
        var currentOp = ""
        var isFirst = true

        for (part in mulDivParts) {
            when (part) {
                "×" -> currentOp = "×"
                "÷" -> currentOp = "÷"
                else -> {
                    val value = part.toDoubleOrNull() ?: 0.0
                    if (isFirst) {
                        result = value
                        isFirst = false
                    } else {
                        result = when (currentOp) {
                            "×" -> result * value
                            "÷" -> {
                                if (value == 0.0) throw ArithmeticException("Cannot divide by zero")
                                result / value
                            }
                            else -> value
                        }
                    }
                }
            }
        }

        return result
    }
    
    private fun performCalculation(
        previousValue: Double,
        currentValue: Double,
        operation: Operation
    ): Result<Double> {
        return try {
            val result = when (operation) {
                Operation.ADD -> previousValue + currentValue
                Operation.SUBTRACT -> previousValue - currentValue
                Operation.MULTIPLY -> previousValue * currentValue
                Operation.DIVIDE -> {
                    if (currentValue == 0.0) {
                        throw ArithmeticException("Cannot divide by zero")
                    }
                    previousValue / currentValue
                }
                Operation.POWER -> previousValue.pow(currentValue)
                Operation.PERCENTAGE -> (previousValue * currentValue) / 100
            }
            
            if (result.isInfinite() || result.isNaN()) {
                throw ArithmeticException("Invalid calculation result")
            }
            
            Result.success(result)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    private fun formatNumber(number: Double): String {
        return if (number == number.toLong().toDouble()) {
            number.toLong().toString()
        } else {
            decimalFormat.format(number)
        }
    }
}
