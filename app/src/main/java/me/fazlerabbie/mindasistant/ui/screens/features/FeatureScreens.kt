package me.fazlerabbie.mindasistant.ui.screens.features

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import me.fazlerabbie.mindasistant.ui.screens.features.duereminder.DueReminderMainScreen
import me.fazlerabbie.mindasistant.ui.screens.features.todolist.TodoListMainScreen
import me.fazlerabbie.mindasistant.ui.screens.features.calculator.CalculatorMainScreen
import me.fazlerabbie.mindasistant.ui.screens.UserSettingsScreen

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun FeatureScreen(
    title: String,
    icon: ImageVector,
    gradientColors: List<Color>,
    onBackClick: () -> Unit,
    content: @Composable () -> Unit = {
        ComingSoonContent(title = title)
    }
) {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(
                brush = Brush.verticalGradient(
                    colors = listOf(
                        Color(0xFFF8F9FA),
                        Color(0xFFE9ECEF)
                    )
                )
            )
    ) {
        // Top App Bar
        TopAppBar(
            title = {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        imageVector = icon,
                        contentDescription = title,
                        modifier = Modifier.size(24.dp),
                        tint = MaterialTheme.colorScheme.primary
                    )
                    Spacer(modifier = Modifier.width(12.dp))
                    Text(
                        text = title,
                        fontSize = 20.sp,
                        fontWeight = FontWeight.Bold
                    )
                }
            },
            navigationIcon = {
                IconButton(onClick = onBackClick) {
                    Icon(
                        imageVector = Icons.Default.ArrowBack,
                        contentDescription = "Back"
                    )
                }
            },
            colors = TopAppBarDefaults.topAppBarColors(
                containerColor = Color.Transparent
            )
        )

        // Content
        content()
    }
}

@Composable
private fun ComingSoonContent(title: String) {
    Box(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        contentAlignment = Alignment.Center
    ) {
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(32.dp),
            shape = RoundedCornerShape(24.dp),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surface
            ),
            elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(32.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    text = "🚀",
                    fontSize = 64.sp,
                    textAlign = TextAlign.Center
                )
                Spacer(modifier = Modifier.height(16.dp))
                Text(
                    text = "$title Coming Soon!",
                    fontSize = 24.sp,
                    fontWeight = FontWeight.Bold,
                    textAlign = TextAlign.Center,
                    color = MaterialTheme.colorScheme.onSurface
                )
                Spacer(modifier = Modifier.height(8.dp))
                Text(
                    text = "We're working hard to bring you this amazing feature. Stay tuned for updates!",
                    fontSize = 16.sp,
                    textAlign = TextAlign.Center,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    lineHeight = 24.sp
                )
            }
        }
    }
}

// Individual feature screens
@Composable
fun DueRemainderScreen(onBackClick: () -> Unit) {
    DueReminderMainScreen(onBackClick = onBackClick)
}

@Composable
fun TodoListScreen(onBackClick: () -> Unit) {
    TodoListMainScreen(onBackClick = onBackClick)
}

@Composable
fun CalculatorScreen(onBackClick: () -> Unit) {
    CalculatorMainScreen(onBackClick = onBackClick)
}

@Composable
fun NotepadScreen(onBackClick: () -> Unit) {
    FeatureScreen(
        title = "Notepad",
        icon = Icons.Default.Edit,
        gradientColors = listOf(Color(0xFFf093fb), Color(0xFFf5576c)),
        onBackClick = onBackClick
    )
}

@Composable
fun TipsAndTricksScreen(onBackClick: () -> Unit) {
    FeatureScreen(
        title = "Tips and Tricks",
        icon = Icons.Default.Lightbulb,
        gradientColors = listOf(Color(0xFFFFB347), Color(0xFFFF8C00)), // Vibrant orange
        onBackClick = onBackClick
    )
}

@Composable
fun MedicineIndexScreen(onBackClick: () -> Unit) {
    FeatureScreen(
        title = "Medicine Index",
        icon = Icons.Default.LocalPharmacy,
        gradientColors = listOf(Color(0xFF00CED1), Color(0xFF20B2AA)), // Turquoise/Teal
        onBackClick = onBackClick
    )
}

@Composable
fun FeatureRequestScreen(onBackClick: () -> Unit) {
    FeatureScreen(
        title = "Feature Request",
        icon = Icons.Default.Add,
        gradientColors = listOf(Color(0xFF9370DB), Color(0xFF8A2BE2)), // Purple
        onBackClick = onBackClick
    )
}

@Composable
fun LifeMomentScreen(onBackClick: () -> Unit) {
    FeatureScreen(
        title = "Life Moment",
        icon = Icons.Default.PhotoCamera,
        gradientColors = listOf(Color(0xFFFF69B4), Color(0xFFFF1493)), // Hot pink
        onBackClick = onBackClick
    )
}

@Composable
fun UserSettingsScreenWrapper(onBackClick: () -> Unit) {
    UserSettingsScreen(onBackClick = onBackClick)
}
