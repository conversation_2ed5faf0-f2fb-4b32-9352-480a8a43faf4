package me.fazlerabbie.mindasistant.ui.components

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import me.fazlerabbie.mindasistant.R
import me.fazlerabbie.mindasistant.ui.theme.MindAsistantTheme
import me.fazlerabbie.mindasistant.ui.theme.RedPrimary
import me.fazlerabbie.mindasistant.ui.theme.RedSecondary

@Composable
fun MindAssistantLogo(
    modifier: Modifier = Modifier,
    showText: Boolean = true,
    logoSize: Int = 80
) {
    Column(
        modifier = modifier,
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        // Logo with actual image
        Image(
            painter = painterResource(id = R.drawable.mindasistantlogo),
            contentDescription = "Mind Assistant Logo",
            modifier = Modifier
                .size(logoSize.dp)
                .clip(RoundedCornerShape(16.dp)),
            contentScale = ContentScale.Fit
        )
        
        if (showText) {
            Spacer(modifier = Modifier.height(16.dp))
            // App name with styled text
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "Mind ",
                    style = MaterialTheme.typography.headlineMedium.copy(
                        fontWeight = FontWeight.Bold,
                        color = MaterialTheme.colorScheme.onBackground
                    )
                )
                Text(
                    text = "Assistant",
                    style = MaterialTheme.typography.headlineMedium.copy(
                        fontWeight = FontWeight.Bold,
                        color = RedPrimary
                    )
                )
            }
        }
    }
}

// Example: How to show local images
@Composable
fun LocalImageExample(
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier.padding(16.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // Method 1: Using drawable resource (most common)
        Image(
            painter = painterResource(id = android.R.drawable.ic_menu_camera), // Example system icon
            contentDescription = "Local image from drawable",
            modifier = Modifier
                .size(100.dp)
                .clip(RoundedCornerShape(8.dp)),
            contentScale = ContentScale.Crop
        )

        // Method 2: Image with background and styling
        Box(
            modifier = Modifier
                .size(120.dp)
                .clip(RoundedCornerShape(16.dp))
                .background(Color.Gray.copy(alpha = 0.1f))
        ) {
            Image(
                painter = painterResource(id = android.R.drawable.ic_menu_gallery), // Example system icon
                contentDescription = "Styled local image",
                modifier = Modifier.fillMaxSize(),
                contentScale = ContentScale.Fit
            )
        }

        // Method 3: Circular image
        Image(
            painter = painterResource(id = android.R.drawable.ic_menu_info_details), // Example system icon
            contentDescription = "Circular image",
            modifier = Modifier
                .size(80.dp)
                .clip(androidx.compose.foundation.shape.CircleShape),
            contentScale = ContentScale.Crop
        )
    }
}

@Preview(showBackground = true)
@Composable
fun MindAssistantLogoPreview() {
    MindAsistantTheme {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(32.dp),
            verticalArrangement = Arrangement.spacedBy(32.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            MindAssistantLogo()
            MindAssistantLogo(showText = false, logoSize = 60)
            MindAssistantLogo(logoSize = 100)
        }
    }
}

@Preview(showBackground = true)
@Composable
fun LocalImageExamplePreview() {
    MindAsistantTheme {
        LocalImageExample()
    }
}
