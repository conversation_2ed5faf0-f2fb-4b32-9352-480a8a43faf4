package me.fazlerabbie.mindasistant.ui.screens.features.calculator

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Calculate
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import me.fazlerabbie.mindasistant.ui.screens.features.calculator.components.CalculatorButton
import me.fazlerabbie.mindasistant.ui.screens.features.calculator.components.CalculatorButtonRow
import me.fazlerabbie.mindasistant.ui.screens.features.calculator.components.CalculatorDisplay
import me.fazlerabbie.mindasistant.ui.screens.features.calculator.components.ButtonType

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun CalculatorMainScreen(
    onBackClick: () -> Unit,
    viewModel: CalculatorViewModel = viewModel()
) {
    val state by viewModel.state.collectAsState()
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(Color.White) // Light theme white background
    ) {
        // Top App Bar with proper header
        TopAppBar(
            title = {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        imageVector = Icons.Default.Calculate,
                        contentDescription = "Calculator",
                        modifier = Modifier.size(24.dp),
                        tint = MaterialTheme.colorScheme.primary
                    )
                    Spacer(modifier = Modifier.width(12.dp))
                    Text(
                        text = "Calculator",
                        fontSize = 20.sp,
                        fontWeight = FontWeight.Bold
                    )
                }
            },
            navigationIcon = {
                IconButton(onClick = onBackClick) {
                    Icon(
                        imageVector = Icons.Default.ArrowBack,
                        contentDescription = "Back"
                    )
                }
            },
            colors = TopAppBarDefaults.topAppBarColors(
                containerColor = Color.Transparent
            )
        )
        
        // Calculator content
        Column(
            modifier = Modifier.fillMaxSize()
        ) {
            // Display takes up top portion
            CalculatorDisplay(
                display = state.display,
                operation = state.operation?.symbol,
                previousValue = if (state.operation != null) state.previousValue.toString() else null,
                error = state.error,
                history = state.history,
                currentExpression = state.currentExpression,
                onClearHistory = viewModel::clearHistory,
                modifier = Modifier.weight(1f)
            )

            // Subtle divider line
            HorizontalDivider(
                color = Color(0xFFE0E0E0),
                thickness = 1.dp,
                modifier = Modifier.padding(horizontal = 16.dp)
            )

            // Button area - Light theme layout
            Column(
                modifier = Modifier.padding(16.dp),
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                // Row 1: AC, +/-, %, ÷ (iPhone layout)
                CalculatorButtonRow(
                    buttons = listOf(
                        "AC" to viewModel::onAllClearClick,
                        "+/-" to viewModel::onPlusMinusClick,
                        "%" to viewModel::onPercentageClick,
                        "÷" to { viewModel.onOperationClick(Operation.DIVIDE) }
                    ),
                    buttonTypes = listOf(
                        ButtonType.FUNCTION,
                        ButtonType.FUNCTION,
                        ButtonType.FUNCTION,
                        ButtonType.OPERATION
                    ),
                    highlightedButtons = listOf(
                        false,
                        false,
                        false,
                        state.activeOperation == Operation.DIVIDE
                    )
                )

                // Row 2: 7, 8, 9, ×
                CalculatorButtonRow(
                    buttons = listOf(
                        "7" to { viewModel.onNumberClick("7") },
                        "8" to { viewModel.onNumberClick("8") },
                        "9" to { viewModel.onNumberClick("9") },
                        "×" to { viewModel.onOperationClick(Operation.MULTIPLY) }
                    ),
                    buttonTypes = listOf(
                        ButtonType.NUMBER,
                        ButtonType.NUMBER,
                        ButtonType.NUMBER,
                        ButtonType.OPERATION
                    ),
                    highlightedButtons = listOf(
                        false,
                        false,
                        false,
                        state.activeOperation == Operation.MULTIPLY
                    )
                )

                // Row 3: 4, 5, 6, -
                CalculatorButtonRow(
                    buttons = listOf(
                        "4" to { viewModel.onNumberClick("4") },
                        "5" to { viewModel.onNumberClick("5") },
                        "6" to { viewModel.onNumberClick("6") },
                        "-" to { viewModel.onOperationClick(Operation.SUBTRACT) }
                    ),
                    buttonTypes = listOf(
                        ButtonType.NUMBER,
                        ButtonType.NUMBER,
                        ButtonType.NUMBER,
                        ButtonType.OPERATION
                    ),
                    highlightedButtons = listOf(
                        false,
                        false,
                        false,
                        state.activeOperation == Operation.SUBTRACT
                    )
                )

                // Row 4: 1, 2, 3, +
                CalculatorButtonRow(
                    buttons = listOf(
                        "1" to { viewModel.onNumberClick("1") },
                        "2" to { viewModel.onNumberClick("2") },
                        "3" to { viewModel.onNumberClick("3") },
                        "+" to { viewModel.onOperationClick(Operation.ADD) }
                    ),
                    buttonTypes = listOf(
                        ButtonType.NUMBER,
                        ButtonType.NUMBER,
                        ButtonType.NUMBER,
                        ButtonType.OPERATION
                    ),
                    highlightedButtons = listOf(
                        false,
                        false,
                        false,
                        state.activeOperation == Operation.ADD
                    )
                )

                // Row 5: 0 (wide), ., = (iPhone layout)
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceEvenly,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    // Wide 0 button (like iPhone)
                    CalculatorButton(
                        text = "0",
                        onClick = { viewModel.onNumberClick("0") },
                        type = ButtonType.ZERO,
                        isWide = true
                    )

                    // Decimal point
                    CalculatorButton(
                        text = ".",
                        onClick = viewModel::onDecimalClick,
                        type = ButtonType.NUMBER
                    )

                    // Equals
                    CalculatorButton(
                        text = "=",
                        onClick = viewModel::onEqualsClick,
                        type = ButtonType.OPERATION
                    )
                }
            }
        }
    }
}
