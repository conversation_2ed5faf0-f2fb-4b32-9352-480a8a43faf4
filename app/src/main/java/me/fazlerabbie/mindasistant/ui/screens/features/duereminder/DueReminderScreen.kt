package me.fazlerabbie.mindasistant.ui.screens.features.duereminder

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.lifecycle.viewmodel.compose.viewModel
import com.google.firebase.auth.FirebaseAuth
import me.fazlerabbie.mindasistant.data.model.DueReminder
import me.fazlerabbie.mindasistant.data.model.DueReminderFilter
import me.fazlerabbie.mindasistant.data.model.PaymentRecord
import me.fazlerabbie.mindasistant.ui.screens.features.duereminder.components.*
import java.util.*

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun DueReminderMainScreen(
    onBackClick: () -> Unit,
    viewModel: DueReminderViewModel = viewModel()
) {
    val context = LocalContext.current
    val currentUser = FirebaseAuth.getInstance().currentUser
    val userId = currentUser?.uid ?: ""

    // Debug info
    println("DueReminderMainScreen: Screen loaded successfully")
    println("DueReminderMainScreen: User ID = $userId")

    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    val selectedFilter by viewModel.selectedFilter.collectAsStateWithLifecycle()
    val filteredReminders by viewModel.filteredReminders.collectAsStateWithLifecycle()

    var showAddReminderDialog by remember { mutableStateOf(false) }
    var showPaymentHistoryDialog by remember { mutableStateOf(false) }
    var showAddPaymentDialog by remember { mutableStateOf(false) }
    var showEditReminderDialog by remember { mutableStateOf(false) }
    var showDeleteConfirmDialog by remember { mutableStateOf(false) }
    var showNotesDialog by remember { mutableStateOf(false) }
    var selectedReminder by remember { mutableStateOf<DueReminder?>(null) }
    var paymentHistory by remember { mutableStateOf<List<PaymentRecord>>(emptyList()) }

    // Load reminders when screen opens
    LaunchedEffect(userId) {
        if (userId.isNotEmpty()) {
            println("Loading due reminders for user: $userId")
            viewModel.loadDueReminders(userId)
        } else {
            println("No user authenticated")
        }
    }

    // Handle messages and errors
    LaunchedEffect(uiState.message) {
        uiState.message?.let { message ->
            println("Success message: $message")
            viewModel.clearMessage()
        }
    }

    LaunchedEffect(uiState.error) {
        uiState.error?.let { error ->
            println("Error: $error")
            viewModel.clearError()
        }
    }
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(
                brush = Brush.verticalGradient(
                    colors = listOf(
                        Color(0xFFF8F9FA),
                        Color(0xFFE9ECEF)
                    )
                )
            )
    ) {
        // Top App Bar
        TopAppBar(
            title = {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        imageVector = Icons.Default.Notifications,
                        contentDescription = "Due Remainder",
                        modifier = Modifier.size(24.dp),
                        tint = MaterialTheme.colorScheme.primary
                    )
                    Spacer(modifier = Modifier.width(12.dp))
                    Text(
                        text = "Due Remainder",
                        fontSize = 20.sp,
                        fontWeight = FontWeight.Bold
                    )
                }
            },
            navigationIcon = {
                IconButton(onClick = onBackClick) {
                    Icon(
                        imageVector = Icons.Default.ArrowBack,
                        contentDescription = "Back"
                    )
                }
            },
            actions = {
                IconButton(
                    onClick = { showAddReminderDialog = true }
                ) {
                    Icon(
                        imageVector = Icons.Default.Add,
                        contentDescription = "Add Reminder",
                        tint = MaterialTheme.colorScheme.primary
                    )
                }
            },
            colors = TopAppBarDefaults.topAppBarColors(
                containerColor = Color.Transparent
            )
        )
        
        // Filter tabs - Single row
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp, vertical = 12.dp),
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            FilterTab(
                text = "Today",
                isSelected = selectedFilter == DueReminderFilter.TODAY,
                onClick = { viewModel.setFilter(DueReminderFilter.TODAY) },
                modifier = Modifier.weight(1f)
            )
            FilterTab(
                text = "Yesterday",
                isSelected = selectedFilter == DueReminderFilter.YESTERDAY,
                onClick = { viewModel.setFilter(DueReminderFilter.YESTERDAY) },
                modifier = Modifier.weight(1f)
            )
            FilterTab(
                text = "Complete",
                isSelected = selectedFilter == DueReminderFilter.COMPLETE,
                onClick = { viewModel.setFilter(DueReminderFilter.COMPLETE) },
                modifier = Modifier.weight(1f)
            )
            FilterTab(
                text = "Upcoming",
                isSelected = selectedFilter == DueReminderFilter.UPCOMING,
                onClick = { viewModel.setFilter(DueReminderFilter.UPCOMING) },
                modifier = Modifier.weight(1f)
            )
        }
        
        // Content
        Box(
            modifier = Modifier
                .fillMaxSize()
                .weight(1f)
        ) {
            if (uiState.isLoading) {
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    CircularProgressIndicator()
                }
            } else if (filteredReminders.isEmpty()) {
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    Column(
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Text(
                            text = "📋",
                            fontSize = 64.sp
                        )
                        Spacer(modifier = Modifier.height(16.dp))
                        Text(
                            text = "No due reminders found",
                            fontSize = 18.sp,
                            fontWeight = FontWeight.Medium,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                        Spacer(modifier = Modifier.height(8.dp))
                        Text(
                            text = "Tap the + button to add your first reminder",
                            fontSize = 14.sp,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                        Spacer(modifier = Modifier.height(16.dp))
                        Button(
                            onClick = { showAddReminderDialog = true }
                        ) {
                            Text("Add Due Reminder")
                        }
                    }
                }
            } else {
                LazyColumn(
                    modifier = Modifier.fillMaxSize(),
                    contentPadding = PaddingValues(bottom = 16.dp)
                ) {
                    items(filteredReminders) { reminder ->
                        DueReminderCard(
                            dueReminder = reminder,
                            onViewPaymentHistory = { reminderId ->
                                selectedReminder = reminder
                                showPaymentHistoryDialog = true
                            },
                            onAddPayment = { reminder ->
                                selectedReminder = reminder
                                showAddPaymentDialog = true
                            },
                            onUpdateNextDate = { reminder ->
                                // Set reminder for tomorrow (next day)
                                val calendar = Calendar.getInstance()
                                calendar.add(Calendar.DAY_OF_MONTH, 1)
                                calendar.set(Calendar.HOUR_OF_DAY, 9) // Set to 9 AM tomorrow
                                calendar.set(Calendar.MINUTE, 0)
                                calendar.set(Calendar.SECOND, 0)
                                calendar.set(Calendar.MILLISECOND, 0)

                                val updatedReminder = reminder.copy(
                                    dueDate = calendar.timeInMillis,
                                    nextReminderDate = calendar.timeInMillis
                                )
                                println("Setting reminder for tomorrow: ${reminder.name}")
                                viewModel.updateDueReminder(updatedReminder)
                            },
                            onEditReminder = { reminder ->
                                selectedReminder = reminder
                                showEditReminderDialog = true
                            },
                            onDeleteReminder = { reminder ->
                                selectedReminder = reminder
                                showDeleteConfirmDialog = true
                            },
                            onViewNotes = { reminder ->
                                selectedReminder = reminder
                                showNotesDialog = true
                            }
                        )
                    }
                }
            }
        }
    }

    // Dialogs
    AddDueReminderDialog(
        isVisible = showAddReminderDialog,
        onDismiss = { showAddReminderDialog = false },
        onSave = { reminder ->
            println("Saving due reminder: ${reminder.name}, Amount: ${reminder.dueAmount}")
            viewModel.saveDueReminder(reminder)
            showAddReminderDialog = false
        },
        userId = userId
    )

    selectedReminder?.let { reminder ->
        AddPaymentDialog(
            isVisible = showAddPaymentDialog,
            onDismiss = {
                showAddPaymentDialog = false
                selectedReminder = null
            },
            onSave = { paymentRecord ->
                println("Saving payment: ${paymentRecord.amount} for ${reminder.name} (ID: ${reminder.id})")
                println("Payment record details: $paymentRecord")
                viewModel.savePaymentRecord(paymentRecord)
                showAddPaymentDialog = false
                selectedReminder = null
            },
            dueReminder = reminder,
            userId = userId
        )

        PaymentHistoryDialog(
            isVisible = showPaymentHistoryDialog,
            onDismiss = {
                showPaymentHistoryDialog = false
                selectedReminder = null
                paymentHistory = emptyList()
            },
            paymentRecords = paymentHistory,
            customerName = reminder.name
        )
    }

    // Edit Reminder Dialog
    selectedReminder?.let { reminder ->
        key(reminder.id) { // Force recomposition when reminder changes
            AddDueReminderDialog(
                isVisible = showEditReminderDialog,
                onDismiss = {
                    showEditReminderDialog = false
                    selectedReminder = null
                },
                onSave = { updatedReminder ->
                    println("Updating due reminder: ${updatedReminder.name}, ID: ${updatedReminder.id}")
                    viewModel.updateDueReminder(updatedReminder)
                    showEditReminderDialog = false
                    selectedReminder = null
                },
                userId = userId,
                existingReminder = reminder
            )
        }
    }

    // Delete Confirmation Dialog
    if (showDeleteConfirmDialog) {
        AlertDialog(
            onDismissRequest = { showDeleteConfirmDialog = false },
            title = {
                Text(
                    text = "Delete Reminder",
                    fontWeight = FontWeight.SemiBold
                )
            },
            text = {
                Text(
                    text = "Are you sure you want to delete \"${selectedReminder?.name}\"? This action cannot be undone.",
                    fontSize = 14.sp
                )
            },
            confirmButton = {
                Button(
                    onClick = {
                        selectedReminder?.let { reminder ->
                            println("Deleting due reminder: ${reminder.name}")
                            viewModel.deleteDueReminder(reminder.id)
                        }
                        showDeleteConfirmDialog = false
                        selectedReminder = null
                    },
                    colors = ButtonDefaults.buttonColors(
                        containerColor = Color(0xFFDC2626)
                    )
                ) {
                    Text("Delete", color = Color.White)
                }
            },
            dismissButton = {
                TextButton(
                    onClick = { showDeleteConfirmDialog = false }
                ) {
                    Text("Cancel")
                }
            }
        )
    }

    // Notes Dialog
    if (showNotesDialog && selectedReminder != null) {
        AlertDialog(
            onDismissRequest = { showNotesDialog = false },
            title = {
                Text(
                    text = "Notes",
                    fontWeight = FontWeight.SemiBold
                )
            },
            text = {
                Text(
                    text = selectedReminder!!.note.ifBlank { "No notes available" },
                    fontSize = 14.sp
                )
            },
            confirmButton = {
                TextButton(
                    onClick = { showNotesDialog = false }
                ) {
                    Text("Close")
                }
            }
        )
    }

    // Load payment history when dialog is shown
    LaunchedEffect(showPaymentHistoryDialog, selectedReminder) {
        if (showPaymentHistoryDialog && selectedReminder != null) {
            println("Loading payment history for: ${selectedReminder!!.name} (ID: ${selectedReminder!!.id})")
            try {
                val history = viewModel.getPaymentHistory(selectedReminder!!.id)
                paymentHistory = history
                println("Payment history loaded: ${history.size} records found")
                history.forEach { payment ->
                    println("Payment: ${payment.amount} on ${java.util.Date(payment.paymentDate)}")
                }
            } catch (e: Exception) {
                println("Error loading payment history: ${e.message}")
                e.printStackTrace()
            }
        }
    }
}

@Composable
private fun FilterTab(
    text: String,
    isSelected: Boolean,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Button(
        onClick = onClick,
        modifier = modifier.height(32.dp),
        colors = ButtonDefaults.buttonColors(
            containerColor = if (isSelected) {
                when (text) {
                    "Today" -> Color(0xFFE53E3E)
                    "Yesterday" -> Color(0xFF2196F3)
                    "Upcoming" -> Color(0xFF4CAF50)
                    "Complete" -> Color(0xFF9C27B0)
                    else -> Color(0xFF757575)
                }
            } else {
                Color.Transparent
            },
            contentColor = if (isSelected) Color.White else MaterialTheme.colorScheme.onSurface
        ),
        shape = RoundedCornerShape(16.dp),
        border = if (!isSelected) {
            androidx.compose.foundation.BorderStroke(
                1.dp,
                MaterialTheme.colorScheme.outline.copy(alpha = 0.3f)
            )
        } else null,
        contentPadding = PaddingValues(horizontal = 8.dp, vertical = 4.dp)
    ) {
        Text(
            text = text,
            fontSize = 12.sp,
            fontWeight = if (isSelected) FontWeight.Bold else FontWeight.Normal,
            maxLines = 1
        )
    }
}

