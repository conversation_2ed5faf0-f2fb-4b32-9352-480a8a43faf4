package me.fazlerabbie.mindasistant.ui.screens.features.duereminder

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import me.fazlerabbie.mindasistant.data.model.DueReminder
import me.fazlerabbie.mindasistant.data.model.DueReminderFilter
import me.fazlerabbie.mindasistant.data.model.PaymentRecord
import me.fazlerabbie.mindasistant.firestore.DueReminderService

class DueReminderViewModel(
    private val dueReminderService: DueReminderService = DueReminderService()
) : ViewModel() {
    
    private val _uiState = MutableStateFlow(DueReminderUiState())
    val uiState: StateFlow<DueReminderUiState> = _uiState.asStateFlow()
    
    private val _selectedFilter = MutableStateFlow(DueReminderFilter.TODAY)
    val selectedFilter: StateFlow<DueReminderFilter> = _selectedFilter.asStateFlow()
    
    private val _allReminders = MutableStateFlow<List<DueReminder>>(emptyList())
    
    val filteredReminders: StateFlow<List<DueReminder>> = combine(
        _allReminders,
        _selectedFilter
    ) { reminders, filter ->
        when (filter) {
            DueReminderFilter.TODAY -> reminders.filter { it.isDueToday() && !it.isCompleted }
            DueReminderFilter.YESTERDAY -> reminders.filter { it.isDueYesterday() && !it.isCompleted }
            DueReminderFilter.UPCOMING -> reminders.filter { it.isUpcoming() }
            DueReminderFilter.COMPLETE -> reminders.filter { it.isCompleted }
            DueReminderFilter.ALL -> reminders
        }
    }.stateIn(
        scope = viewModelScope,
        started = SharingStarted.WhileSubscribed(5000),
        initialValue = emptyList()
    )
    
    fun loadDueReminders(userId: String) {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true)
            
            dueReminderService.getDueRemindersFlow(userId)
                .catch { exception ->
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        error = exception.message
                    )
                }
                .collect { reminders ->
                    _allReminders.value = reminders
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        error = null
                    )
                }
        }
    }
    
    fun setFilter(filter: DueReminderFilter) {
        _selectedFilter.value = filter
    }
    
    fun saveDueReminder(dueReminder: DueReminder) {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true)
            
            dueReminderService.saveDueReminder(dueReminder).fold(
                onSuccess = {
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        message = "Due reminder saved successfully"
                    )
                },
                onFailure = { exception ->
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        error = exception.message
                    )
                }
            )
        }
    }
    
    fun updateDueReminder(dueReminder: DueReminder) {
        viewModelScope.launch {
            println("DueReminderViewModel: Updating reminder - ID: ${dueReminder.id}, Name: ${dueReminder.name}")
            dueReminderService.updateDueReminder(dueReminder).fold(
                onSuccess = {
                    println("DueReminderViewModel: Update successful")
                    _uiState.value = _uiState.value.copy(
                        message = "Due reminder updated successfully"
                    )
                },
                onFailure = { exception ->
                    println("DueReminderViewModel: Update failed - ${exception.message}")
                    exception.printStackTrace()
                    _uiState.value = _uiState.value.copy(
                        error = exception.message
                    )
                }
            )
        }
    }
    
    fun deleteDueReminder(reminderId: String) {
        viewModelScope.launch {
            dueReminderService.deleteDueReminder(reminderId).fold(
                onSuccess = {
                    _uiState.value = _uiState.value.copy(
                        message = "Due reminder deleted successfully"
                    )
                },
                onFailure = { exception ->
                    _uiState.value = _uiState.value.copy(
                        error = exception.message
                    )
                }
            )
        }
    }
    
    fun markAsCompleted(reminderId: String) {
        viewModelScope.launch {
            dueReminderService.markAsCompleted(reminderId).fold(
                onSuccess = {
                    _uiState.value = _uiState.value.copy(
                        message = "Marked as completed"
                    )
                },
                onFailure = { exception ->
                    _uiState.value = _uiState.value.copy(
                        error = exception.message
                    )
                }
            )
        }
    }
    
    fun savePaymentRecord(paymentRecord: PaymentRecord) {
        viewModelScope.launch {
            dueReminderService.savePaymentRecord(paymentRecord).fold(
                onSuccess = {
                    _uiState.value = _uiState.value.copy(
                        message = "Payment recorded successfully"
                    )
                },
                onFailure = { exception ->
                    _uiState.value = _uiState.value.copy(
                        error = exception.message
                    )
                }
            )
        }
    }
    
    suspend fun getPaymentHistory(dueReminderId: String): List<PaymentRecord> {
        return dueReminderService.getPaymentRecords(dueReminderId).getOrElse { emptyList() }
    }
    
    fun clearMessage() {
        _uiState.value = _uiState.value.copy(message = null)
    }
    
    fun clearError() {
        _uiState.value = _uiState.value.copy(error = null)
    }
}

data class DueReminderUiState(
    val isLoading: Boolean = false,
    val error: String? = null,
    val message: String? = null
)
