package me.fazlerabbie.mindasistant.ui.screens.features.calculator.components

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp

enum class ButtonType {
    NUMBER,
    OPERATION,
    FUNCTION,
    ZERO // Special type for the 0 button which is wider
}

@Composable
fun CalculatorButton(
    text: String,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    type: ButtonType = ButtonType.NUMBER,
    isWide: Boolean = false,
    isHighlighted: Boolean = false
) {
    val backgroundColor = when {
        isHighlighted -> Color(0xFFFF9500) // Orange background when highlighted
        type == ButtonType.NUMBER || type == ButtonType.ZERO -> Color(0xFFF5F5F5) // Light gray for numbers
        type == ButtonType.OPERATION -> Color(0xFFFF9500) // Orange for operations
        type == ButtonType.FUNCTION -> Color(0xFFE0E0E0) // Medium gray for functions
        else -> Color(0xFFF5F5F5)
    }

    val textColor = when {
        isHighlighted -> Color.White // White text when highlighted
        type == ButtonType.OPERATION -> Color.White // White text on orange
        else -> Color.Black // Black text for light backgrounds
    }

    val buttonSize = 80.dp
    val buttonModifier = if (isWide) {
        modifier
            .width(buttonSize * 2 + 8.dp) // Double width plus spacing
            .height(buttonSize)
    } else {
        modifier
            .size(buttonSize)
    }

    Box(
        modifier = buttonModifier
            .background(
                color = backgroundColor,
                shape = CircleShape
            )
            .clickable { onClick() }
            .then(
                // Add subtle shadow for light theme
                if (backgroundColor != Color(0xFFFF9500)) {
                    Modifier.background(
                        color = Color.Black.copy(alpha = 0.05f),
                        shape = CircleShape
                    )
                } else Modifier
            ),
        contentAlignment = if (isWide) Alignment.CenterStart else Alignment.Center
    ) {
        Text(
            text = text,
            fontSize = 32.sp,
            fontWeight = FontWeight.Normal,
            color = textColor,
            modifier = if (isWide) Modifier.padding(start = 24.dp) else Modifier
        )
    }
}

@Composable
fun CalculatorButtonRow(
    buttons: List<Pair<String, () -> Unit>>,
    buttonTypes: List<ButtonType> = List(buttons.size) { ButtonType.NUMBER },
    highlightedButtons: List<Boolean> = List(buttons.size) { false },
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceEvenly
    ) {
        buttons.forEachIndexed { index, (text, onClick) ->
            CalculatorButton(
                text = text,
                onClick = onClick,
                type = buttonTypes.getOrElse(index) { ButtonType.NUMBER },
                isHighlighted = highlightedButtons.getOrElse(index) { false }
            )
        }
    }
}
