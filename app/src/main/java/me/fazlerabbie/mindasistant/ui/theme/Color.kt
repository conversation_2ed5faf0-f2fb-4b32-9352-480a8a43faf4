package me.fazlerabbie.mindasistant.ui.theme

import androidx.compose.ui.graphics.Color

// Modern red gradient colors inspired by the Mind Assistant logo
val RedPrimary = Color(0xFFE53E3E)
val RedSecondary = Color(0xFFFF6B6B)
val RedLight = Color(0xFFFFE5E5)
val RedDark = Color(0xFFC53030)

// Neutral colors for modern design
val Gray50 = Color(0xFFF9FAFB)
val Gray100 = Color(0xFFF3F4F6)
val Gray200 = Color(0xFFE5E7EB)
val Gray300 = Color(0xFFD1D5DB)
val Gray400 = Color(0xFF9CA3AF)
val Gray500 = Color(0xFF6B7280)
val Gray600 = Color(0xFF4B5563)
val Gray700 = Color(0xFF374151)
val Gray800 = Color(0xFF1F2937)
val Gray900 = Color(0xFF111827)

// Background colors
val BackgroundLight = Color(0xFFFFFFF)
val BackgroundDark = Color(0xFF0F0F0F)
val SurfaceLight = Color(0xFFFFFFFF)
val SurfaceDark = Color(0xFF1A1A1A)

// Text colors
val TextPrimary = Color(0xFF1F2937)
val TextSecondary = Color(0xFF6B7280)
val TextOnPrimary = Color(0xFFFFFFFF)

// Legacy colors (keeping for compatibility)
val Purple80 = Color(0xFFD0BCFF)
val PurpleGrey80 = Color(0xFFCCC2DC)
val Pink80 = Color(0xFFEFB8C8)

val Purple40 = Color(0xFF6650a4)
val PurpleGrey40 = Color(0xFF625b71)
val Pink40 = Color(0xFF7D5260)