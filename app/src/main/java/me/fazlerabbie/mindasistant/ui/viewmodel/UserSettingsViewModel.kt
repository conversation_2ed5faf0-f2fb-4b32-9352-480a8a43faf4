package me.fazlerabbie.mindasistant.ui.viewmodel

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.viewModelScope
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import me.fazlerabbie.mindasistant.auth.SyncFrequency
import me.fazlerabbie.mindasistant.auth.UserProfile
import me.fazlerabbie.mindasistant.data.local.MindAssistantDatabase
import me.fazlerabbie.mindasistant.data.local.entity.toUserProfile
import me.fazlerabbie.mindasistant.sync.SyncManager

data class UserSettingsUiState(
    val userProfile: UserProfile? = null,
    val syncFrequency: SyncFrequency = SyncFrequency.WEEKLY,
    val autoSyncEnabled: Boolean = true,
    val syncOnlyOnWifi: Boolean = true,
    val syncNotificationsEnabled: Boolean = true,
    val lastSyncTime: Long = 0L,
    val isLoading: Boolean = false,
    val errorMessage: String? = null,
    val isSyncing: Boolean = false
)

class UserSettingsViewModel(application: Application) : AndroidViewModel(application) {
    
    private val database = MindAssistantDatabase.getDatabase(application)
    private val syncManager = SyncManager(application)
    
    private val _uiState = MutableStateFlow(UserSettingsUiState())
    val uiState: StateFlow<UserSettingsUiState> = _uiState.asStateFlow()
    
    /**
     * Load user settings from local database
     */
    fun loadUserSettings(userId: String) {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true)
            
            try {
                val userProfileEntity = database.userProfileDao().getUserProfile(userId)
                val userProfile = userProfileEntity?.toUserProfile()
                
                _uiState.value = _uiState.value.copy(
                    userProfile = userProfile,
                    syncFrequency = userProfile?.syncFrequency ?: SyncFrequency.WEEKLY,
                    autoSyncEnabled = userProfile?.autoSyncEnabled ?: true,
                    syncOnlyOnWifi = userProfile?.syncOnlyOnWifi ?: true,
                    syncNotificationsEnabled = userProfile?.syncNotificationsEnabled ?: true,
                    lastSyncTime = userProfile?.lastSyncTime ?: 0L,
                    isLoading = false,
                    errorMessage = null
                )
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    errorMessage = "Failed to load settings: ${e.message}"
                )
            }
        }
    }
    
    /**
     * Update sync frequency
     */
    fun updateSyncFrequency(userId: String, newFrequency: SyncFrequency) {
        viewModelScope.launch {
            try {
                syncManager.updateSyncFrequency(userId, newFrequency)
                _uiState.value = _uiState.value.copy(
                    syncFrequency = newFrequency,
                    errorMessage = null
                )
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    errorMessage = "Failed to update sync frequency: ${e.message}"
                )
            }
        }
    }
    
    /**
     * Enable/disable auto sync
     */
    fun setAutoSyncEnabled(userId: String, enabled: Boolean) {
        viewModelScope.launch {
            try {
                syncManager.setAutoSyncEnabled(userId, enabled)
                _uiState.value = _uiState.value.copy(
                    autoSyncEnabled = enabled,
                    errorMessage = null
                )
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    errorMessage = "Failed to update auto sync setting: ${e.message}"
                )
            }
        }
    }
    
    /**
     * Set WiFi only preference
     */
    fun setSyncOnlyOnWifi(userId: String, wifiOnly: Boolean) {
        viewModelScope.launch {
            try {
                syncManager.setSyncOnlyOnWifi(userId, wifiOnly)
                _uiState.value = _uiState.value.copy(
                    syncOnlyOnWifi = wifiOnly,
                    errorMessage = null
                )
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    errorMessage = "Failed to update WiFi setting: ${e.message}"
                )
            }
        }
    }
    
    /**
     * Enable/disable sync notifications
     */
    fun setSyncNotificationsEnabled(userId: String, enabled: Boolean) {
        viewModelScope.launch {
            try {
                syncManager.setSyncNotificationsEnabled(userId, enabled)
                _uiState.value = _uiState.value.copy(
                    syncNotificationsEnabled = enabled,
                    errorMessage = null
                )
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    errorMessage = "Failed to update notification setting: ${e.message}"
                )
            }
        }
    }
    
    /**
     * Trigger manual sync
     */
    fun triggerManualSync(userId: String) {
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.copy(
                    isSyncing = true,
                    errorMessage = null
                )
                
                syncManager.triggerManualSync(userId)
                
                // Note: The actual sync completion will be handled by WorkManager
                // We could observe the work status here if needed
                _uiState.value = _uiState.value.copy(
                    isSyncing = false
                )
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isSyncing = false,
                    errorMessage = "Failed to start sync: ${e.message}"
                )
            }
        }
    }
    
    /**
     * Clear error message
     */
    fun clearError() {
        _uiState.value = _uiState.value.copy(errorMessage = null)
    }
    
    /**
     * Refresh user settings
     */
    fun refreshSettings(userId: String) {
        loadUserSettings(userId)
    }
}
