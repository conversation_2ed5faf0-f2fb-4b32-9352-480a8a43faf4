package me.fazlerabbie.mindasistant.data.local.dao

import androidx.room.*
import kotlinx.coroutines.flow.Flow
import me.fazlerabbie.mindasistant.data.local.entity.DueReminderEntity

@Dao
interface DueReminderDao {
    
    @Query("SELECT * FROM due_reminders WHERE userId = :userId AND isDeleted = 0 ORDER BY dueDate ASC")
    fun getDueRemindersFlow(userId: String): Flow<List<DueReminderEntity>>
    
    @Query("SELECT * FROM due_reminders WHERE userId = :userId AND isDeleted = 0 ORDER BY dueDate ASC")
    suspend fun getDueReminders(userId: String): List<DueReminderEntity>
    
    @Query("SELECT * FROM due_reminders WHERE id = :reminderId AND isDeleted = 0")
    suspend fun getDueReminderById(reminderId: String): DueReminderEntity?
    
    @Query("SELECT * FROM due_reminders WHERE userId = :userId AND isCompleted = :isCompleted AND isDeleted = 0 ORDER BY dueDate ASC")
    suspend fun getDueRemindersByCompletion(userId: String, isCompleted: Boolean): List<DueReminderEntity>
    
    @Query("SELECT * FROM due_reminders WHERE userId = :userId AND dueDate <= :currentTime AND isCompleted = 0 AND isDeleted = 0 ORDER BY dueDate ASC")
    suspend fun getOverdueReminders(userId: String, currentTime: Long): List<DueReminderEntity>
    
    @Query("SELECT * FROM due_reminders WHERE userId = :userId AND nextReminderDate <= :currentTime AND isCompleted = 0 AND isDeleted = 0 ORDER BY nextReminderDate ASC")
    suspend fun getRemindersToNotify(userId: String, currentTime: Long): List<DueReminderEntity>
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertDueReminder(reminder: DueReminderEntity)
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertDueReminders(reminders: List<DueReminderEntity>)
    
    @Update
    suspend fun updateDueReminder(reminder: DueReminderEntity)
    
    @Query("UPDATE due_reminders SET isCompleted = :isCompleted, updatedAt = :updatedAt, isSynced = 0, lastModified = :lastModified WHERE id = :reminderId")
    suspend fun updateReminderCompletion(
        reminderId: String, 
        isCompleted: Boolean, 
        updatedAt: Long,
        lastModified: Long
    )
    
    @Query("UPDATE due_reminders SET dueAmount = :newAmount, updatedAt = :updatedAt, isSynced = 0, lastModified = :lastModified WHERE id = :reminderId")
    suspend fun updateDueAmount(
        reminderId: String, 
        newAmount: Double, 
        updatedAt: Long,
        lastModified: Long
    )
    
    @Query("UPDATE due_reminders SET isDeleted = 1, isSynced = 0, lastModified = :lastModified WHERE id = :reminderId")
    suspend fun markReminderAsDeleted(reminderId: String, lastModified: Long)
    
    @Query("DELETE FROM due_reminders WHERE id = :reminderId")
    suspend fun deleteDueReminder(reminderId: String)
    
    @Query("DELETE FROM due_reminders WHERE userId = :userId")
    suspend fun deleteAllRemindersForUser(userId: String)
    
    // Sync related queries
    @Query("SELECT * FROM due_reminders WHERE userId = :userId AND isSynced = 0")
    suspend fun getUnsyncedReminders(userId: String): List<DueReminderEntity>
    
    @Query("SELECT * FROM due_reminders WHERE userId = :userId AND isDeleted = 1")
    suspend fun getDeletedReminders(userId: String): List<DueReminderEntity>
    
    @Query("UPDATE due_reminders SET isSynced = 1 WHERE id = :reminderId")
    suspend fun markReminderAsSynced(reminderId: String)
    
    @Query("UPDATE due_reminders SET isSynced = 1 WHERE id IN (:reminderIds)")
    suspend fun markRemindersAsSynced(reminderIds: List<String>)
    
    @Query("DELETE FROM due_reminders WHERE isDeleted = 1 AND lastModified < :cutoffTime")
    suspend fun cleanupDeletedReminders(cutoffTime: Long)
    
    // Statistics queries
    @Query("SELECT SUM(dueAmount) FROM due_reminders WHERE userId = :userId AND isCompleted = 0 AND isDeleted = 0")
    suspend fun getTotalDueAmount(userId: String): Double?
    
    @Query("SELECT COUNT(*) FROM due_reminders WHERE userId = :userId AND isDeleted = 0")
    suspend fun getReminderCount(userId: String): Int
    
    @Query("SELECT COUNT(*) FROM due_reminders WHERE userId = :userId AND isCompleted = 0 AND isDeleted = 0")
    suspend fun getPendingReminderCount(userId: String): Int
    
    @Query("SELECT COUNT(*) FROM due_reminders WHERE userId = :userId AND isCompleted = 1 AND isDeleted = 0")
    suspend fun getCompletedReminderCount(userId: String): Int
}
