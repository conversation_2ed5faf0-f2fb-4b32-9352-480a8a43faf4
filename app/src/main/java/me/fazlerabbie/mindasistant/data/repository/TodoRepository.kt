package me.fazlerabbie.mindasistant.data.repository

import android.content.Context
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import me.fazlerabbie.mindasistant.data.local.MindAssistantDatabase
import me.fazlerabbie.mindasistant.data.local.entity.toTodoEntity
import me.fazlerabbie.mindasistant.data.local.entity.toTodoItem
import me.fazlerabbie.mindasistant.data.model.TodoItem
import java.util.UUID

class TodoRepository(context: Context) {
    
    private val database = MindAssistantDatabase.getDatabase(context)
    private val todoDao = database.todoDao()
    
    /**
     * Get todos flow for real-time updates (offline-first)
     */
    fun getTodosFlow(userId: String): Flow<List<TodoItem>> {
        return todoDao.getTodosFlow(userId).map { entities ->
            entities.map { it.toTodoItem() }
        }
    }
    
    /**
     * Get todos (offline-first)
     */
    suspend fun getTodos(userId: String): List<TodoItem> {
        return todoDao.getTodos(userId).map { it.toTodoItem() }
    }
    
    /**
     * Get todo by ID (offline-first)
     */
    suspend fun getTodoById(todoId: String): TodoItem? {
        return todoDao.getTodoById(todoId)?.toTodoItem()
    }
    
    /**
     * Save todo (offline-first)
     */
    suspend fun saveTodo(todoItem: TodoItem): Result<String> {
        return try {
            val todoId = if (todoItem.id.isEmpty()) {
                UUID.randomUUID().toString()
            } else {
                todoItem.id
            }
            
            val todoToSave = todoItem.copy(
                id = todoId,
                updatedAt = System.currentTimeMillis()
            )
            
            val todoEntity = todoToSave.toTodoEntity(isSynced = false)
            todoDao.insertTodo(todoEntity)
            
            Result.success(todoId)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * Update todo (offline-first)
     */
    suspend fun updateTodo(todoItem: TodoItem): Result<Unit> {
        return try {
            val updatedTodo = todoItem.copy(updatedAt = System.currentTimeMillis())
            val todoEntity = updatedTodo.toTodoEntity(isSynced = false)
            todoDao.updateTodo(todoEntity)
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * Toggle todo completion (offline-first)
     */
    suspend fun toggleTodoCompletion(todoId: String, isCompleted: Boolean): Result<Unit> {
        return try {
            val completedAt = if (isCompleted) System.currentTimeMillis() else null
            val updatedAt = System.currentTimeMillis()
            val lastModified = System.currentTimeMillis()
            
            todoDao.updateTodoCompletion(todoId, isCompleted, completedAt, updatedAt, lastModified)
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * Delete todo (offline-first - mark as deleted)
     */
    suspend fun deleteTodo(todoId: String): Result<Unit> {
        return try {
            todoDao.markTodoAsDeleted(todoId, System.currentTimeMillis())
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * Get todos by completion status (offline-first)
     */
    suspend fun getTodosByCompletion(userId: String, isCompleted: Boolean): List<TodoItem> {
        return todoDao.getTodosByCompletion(userId, isCompleted).map { it.toTodoItem() }
    }
    
    /**
     * Get overdue todos (offline-first)
     */
    suspend fun getOverdueTodos(userId: String): List<TodoItem> {
        val currentTime = System.currentTimeMillis()
        return todoDao.getOverdueTodos(userId, currentTime).map { it.toTodoItem() }
    }
    
    /**
     * Get todos for specific date (offline-first)
     */
    suspend fun getTodosForDate(userId: String, startOfDay: Long, endOfDay: Long): List<TodoItem> {
        return todoDao.getTodosForDate(userId, startOfDay, endOfDay).map { it.toTodoItem() }
    }
    
    /**
     * Get todo statistics (offline-first)
     */
    suspend fun getTodoStats(userId: String): TodoStats {
        return TodoStats(
            totalCount = todoDao.getTodoCount(userId),
            pendingCount = todoDao.getPendingTodoCount(userId),
            completedCount = todoDao.getCompletedTodoCount(userId)
        )
    }

    /**
     * Delete completed todos (offline-first)
     */
    suspend fun deleteCompletedTodos(userId: String): Result<Int> {
        return try {
            val completedTodos = todoDao.getTodosByCompletion(userId, true)
            completedTodos.forEach { todo ->
                todoDao.markTodoAsDeleted(todo.id, System.currentTimeMillis())
            }
            Result.success(completedTodos.size)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
}

data class TodoStats(
    val totalCount: Int,
    val pendingCount: Int,
    val completedCount: Int
)
