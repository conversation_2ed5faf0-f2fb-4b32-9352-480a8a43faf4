package me.fazlerabbie.mindasistant.data.local.entity

import androidx.room.Entity
import androidx.room.PrimaryKey
import androidx.room.TypeConverter
import androidx.room.TypeConverters
import me.fazlerabbie.mindasistant.data.model.DueReminder
import me.fazlerabbie.mindasistant.data.model.RepeatType

@Entity(tableName = "due_reminders")
@TypeConverters(DueReminderConverters::class)
data class DueReminderEntity(
    @PrimaryKey
    val id: String,
    val userId: String,
    val name: String,
    val mobileNo: String,
    val dueAmount: Double,
    val originalAmount: Double,
    val dueDate: Long,
    val nextReminderDate: Long,
    val repeat: RepeatType,
    val note: String,
    val isCompleted: Boolean,
    val createdAt: Long,
    val updatedAt: Long,
    // Sync fields
    val isSynced: Boolean = false,
    val isDeleted: Boolean = false,
    val lastModified: Long = System.currentTimeMillis()
)

// Extension functions to convert between DueReminderEntity and DueReminder
fun DueReminderEntity.toDueReminder(): DueReminder {
    return DueReminder(
        id = id,
        userId = userId,
        name = name,
        mobileNo = mobileNo,
        dueAmount = dueAmount,
        originalAmount = originalAmount,
        dueDate = dueDate,
        nextReminderDate = nextReminderDate,
        repeat = repeat,
        note = note,
        isCompleted = isCompleted,
        createdAt = createdAt,
        updatedAt = updatedAt
    )
}

fun DueReminder.toDueReminderEntity(isSynced: Boolean = false, isDeleted: Boolean = false): DueReminderEntity {
    return DueReminderEntity(
        id = id,
        userId = userId,
        name = name,
        mobileNo = mobileNo,
        dueAmount = dueAmount,
        originalAmount = originalAmount,
        dueDate = dueDate,
        nextReminderDate = nextReminderDate,
        repeat = repeat,
        note = note,
        isCompleted = isCompleted,
        createdAt = createdAt,
        updatedAt = updatedAt,
        isSynced = isSynced,
        isDeleted = isDeleted,
        lastModified = System.currentTimeMillis()
    )
}

class DueReminderConverters {
    @TypeConverter
    fun fromRepeatType(repeatType: RepeatType): String {
        return repeatType.name
    }

    @TypeConverter
    fun toRepeatType(repeatType: String): RepeatType {
        return RepeatType.valueOf(repeatType)
    }
}
