package me.fazlerabbie.mindasistant.data.model

data class PaymentRecord(
    val id: String = "",
    val dueReminderId: String = "",
    val userId: String = "",
    val amount: Double = 0.0,
    val paymentDate: Long = System.currentTimeMillis(),
    val note: String = "",
    val paymentMethod: PaymentMethod = PaymentMethod.CASH,
    val createdAt: Long = System.currentTimeMillis()
) {
    // No-argument constructor for Firestore
    constructor() : this("", "", "", 0.0, System.currentTimeMillis(), "", PaymentMethod.CASH, System.currentTimeMillis())
}

enum class PaymentMethod(val displayName: String) {
    CASH("Cash"),
    BANK_TRANSFER("Bank Transfer"),
    MOBILE_BANKING("Mobile Banking"),
    CARD("Card"),
    OTHER("Other")
}
