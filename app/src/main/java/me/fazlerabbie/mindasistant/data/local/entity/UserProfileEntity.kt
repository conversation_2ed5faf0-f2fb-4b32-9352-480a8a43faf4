package me.fazlerabbie.mindasistant.data.local.entity

import androidx.room.Entity
import androidx.room.PrimaryKey
import androidx.room.TypeConverter
import androidx.room.TypeConverters
import me.fazlerabbie.mindasistant.auth.SyncFrequency
import me.fazlerabbie.mindasistant.auth.UserProfile

@Entity(tableName = "user_profiles")
@TypeConverters(UserProfileConverters::class)
data class UserProfileEntity(
    @PrimaryKey
    val uid: String,
    val name: String,
    val email: String,
    val mobile: String,
    val occupation: String,
    val bloodGroup: String?,
    val upazila: String,
    val district: String,
    val createdAt: Long,
    // Sync preferences
    val syncFrequency: SyncFrequency,
    val autoSyncEnabled: Boolean,
    val syncOnlyOnWifi: Boolean,
    val lastSyncTime: Long,
    val syncNotificationsEnabled: Boolean,
    // Sync fields
    val isSynced: Boolean = false,
    val lastModified: Long = System.currentTimeMillis()
)

// Extension functions to convert between UserProfileEntity and UserProfile
fun UserProfileEntity.toUserProfile(): UserProfile {
    return UserProfile(
        uid = uid,
        name = name,
        email = email,
        mobile = mobile,
        occupation = occupation,
        bloodGroup = bloodGroup,
        upazila = upazila,
        district = district,
        createdAt = createdAt,
        syncFrequency = syncFrequency,
        autoSyncEnabled = autoSyncEnabled,
        syncOnlyOnWifi = syncOnlyOnWifi,
        lastSyncTime = lastSyncTime,
        syncNotificationsEnabled = syncNotificationsEnabled
    )
}

fun UserProfile.toUserProfileEntity(isSynced: Boolean = false): UserProfileEntity {
    return UserProfileEntity(
        uid = uid,
        name = name,
        email = email,
        mobile = mobile,
        occupation = occupation,
        bloodGroup = bloodGroup,
        upazila = upazila,
        district = district,
        createdAt = createdAt,
        syncFrequency = syncFrequency,
        autoSyncEnabled = autoSyncEnabled,
        syncOnlyOnWifi = syncOnlyOnWifi,
        lastSyncTime = lastSyncTime,
        syncNotificationsEnabled = syncNotificationsEnabled,
        isSynced = isSynced,
        lastModified = System.currentTimeMillis()
    )
}

class UserProfileConverters {
    @TypeConverter
    fun fromSyncFrequency(syncFrequency: SyncFrequency): String {
        return syncFrequency.name
    }

    @TypeConverter
    fun toSyncFrequency(syncFrequency: String): SyncFrequency {
        return SyncFrequency.valueOf(syncFrequency)
    }
}
