package me.fazlerabbie.mindasistant.data.repository

import android.content.Context
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import me.fazlerabbie.mindasistant.data.local.MindAssistantDatabase
import me.fazlerabbie.mindasistant.data.local.entity.toDueReminder
import me.fazlerabbie.mindasistant.data.local.entity.toDueReminderEntity
import me.fazlerabbie.mindasistant.data.local.entity.toPaymentRecord
import me.fazlerabbie.mindasistant.data.local.entity.toPaymentRecordEntity
import me.fazlerabbie.mindasistant.data.model.DueReminder
import me.fazlerabbie.mindasistant.data.model.PaymentRecord
import java.util.UUID

class DueReminderRepository(context: Context) {
    
    private val database = MindAssistantDatabase.getDatabase(context)
    private val dueReminderDao = database.dueReminderDao()
    private val paymentRecordDao = database.paymentRecordDao()
    
    /**
     * Get due reminders flow for real-time updates (offline-first)
     */
    fun getDueRemindersFlow(userId: String): Flow<List<DueReminder>> {
        return dueReminderDao.getDueRemindersFlow(userId).map { entities ->
            entities.map { it.toDueReminder() }
        }
    }
    
    /**
     * Get due reminders (offline-first)
     */
    suspend fun getDueReminders(userId: String): List<DueReminder> {
        return dueReminderDao.getDueReminders(userId).map { it.toDueReminder() }
    }
    
    /**
     * Get due reminder by ID (offline-first)
     */
    suspend fun getDueReminderById(reminderId: String): DueReminder? {
        return dueReminderDao.getDueReminderById(reminderId)?.toDueReminder()
    }
    
    /**
     * Save due reminder (offline-first)
     */
    suspend fun saveDueReminder(dueReminder: DueReminder): Result<String> {
        return try {
            val reminderId = if (dueReminder.id.isEmpty()) {
                UUID.randomUUID().toString()
            } else {
                dueReminder.id
            }
            
            val reminderToSave = dueReminder.copy(
                id = reminderId,
                updatedAt = System.currentTimeMillis()
            )
            
            val reminderEntity = reminderToSave.toDueReminderEntity(isSynced = false)
            dueReminderDao.insertDueReminder(reminderEntity)
            
            Result.success(reminderId)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * Update due reminder (offline-first)
     */
    suspend fun updateDueReminder(dueReminder: DueReminder): Result<Unit> {
        return try {
            val updatedReminder = dueReminder.copy(updatedAt = System.currentTimeMillis())
            val reminderEntity = updatedReminder.toDueReminderEntity(isSynced = false)
            dueReminderDao.updateDueReminder(reminderEntity)
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * Mark reminder as completed (offline-first)
     */
    suspend fun markReminderAsCompleted(reminderId: String, isCompleted: Boolean): Result<Unit> {
        return try {
            val updatedAt = System.currentTimeMillis()
            val lastModified = System.currentTimeMillis()
            
            dueReminderDao.updateReminderCompletion(reminderId, isCompleted, updatedAt, lastModified)
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * Update due amount (offline-first)
     */
    suspend fun updateDueAmount(reminderId: String, newAmount: Double): Result<Unit> {
        return try {
            val updatedAt = System.currentTimeMillis()
            val lastModified = System.currentTimeMillis()
            
            dueReminderDao.updateDueAmount(reminderId, newAmount, updatedAt, lastModified)
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * Delete due reminder (offline-first - mark as deleted)
     */
    suspend fun deleteDueReminder(reminderId: String): Result<Unit> {
        return try {
            dueReminderDao.markReminderAsDeleted(reminderId, System.currentTimeMillis())
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * Get overdue reminders (offline-first)
     */
    suspend fun getOverdueReminders(userId: String): List<DueReminder> {
        val currentTime = System.currentTimeMillis()
        return dueReminderDao.getOverdueReminders(userId, currentTime).map { it.toDueReminder() }
    }
    
    /**
     * Get reminders to notify (offline-first)
     */
    suspend fun getRemindersToNotify(userId: String): List<DueReminder> {
        val currentTime = System.currentTimeMillis()
        return dueReminderDao.getRemindersToNotify(userId, currentTime).map { it.toDueReminder() }
    }
    
    /**
     * Get total due amount (offline-first)
     */
    suspend fun getTotalDueAmount(userId: String): Double {
        return dueReminderDao.getTotalDueAmount(userId) ?: 0.0
    }
    
    /**
     * Save payment record (offline-first)
     */
    suspend fun savePaymentRecord(paymentRecord: PaymentRecord): Result<String> {
        return try {
            val paymentId = if (paymentRecord.id.isEmpty()) {
                UUID.randomUUID().toString()
            } else {
                paymentRecord.id
            }
            
            val recordToSave = paymentRecord.copy(
                id = paymentId,
                createdAt = System.currentTimeMillis()
            )
            
            val paymentEntity = recordToSave.toPaymentRecordEntity(isSynced = false)
            paymentRecordDao.insertPaymentRecord(paymentEntity)
            
            Result.success(paymentId)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * Get payment records for reminder (offline-first)
     */
    suspend fun getPaymentRecordsForReminder(dueReminderId: String): List<PaymentRecord> {
        return paymentRecordDao.getPaymentRecordsForReminder(dueReminderId).map { it.toPaymentRecord() }
    }
    
    /**
     * Get payment records flow (offline-first)
     */
    fun getPaymentRecordsFlow(userId: String): Flow<List<PaymentRecord>> {
        return paymentRecordDao.getPaymentRecordsFlow(userId).map { entities ->
            entities.map { it.toPaymentRecord() }
        }
    }
    
    /**
     * Delete payment record (offline-first - mark as deleted)
     */
    suspend fun deletePaymentRecord(paymentId: String): Result<Unit> {
        return try {
            paymentRecordDao.markPaymentRecordAsDeleted(paymentId, System.currentTimeMillis())
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * Get reminder statistics (offline-first)
     */
    suspend fun getReminderStats(userId: String): ReminderStats {
        return ReminderStats(
            totalCount = dueReminderDao.getReminderCount(userId),
            pendingCount = dueReminderDao.getPendingReminderCount(userId),
            completedCount = dueReminderDao.getCompletedReminderCount(userId),
            totalDueAmount = getTotalDueAmount(userId),
            totalPaymentAmount = paymentRecordDao.getTotalPaymentAmount(userId) ?: 0.0
        )
    }
}

data class ReminderStats(
    val totalCount: Int,
    val pendingCount: Int,
    val completedCount: Int,
    val totalDueAmount: Double,
    val totalPaymentAmount: Double
)
