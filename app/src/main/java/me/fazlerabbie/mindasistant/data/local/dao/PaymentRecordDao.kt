package me.fazlerabbie.mindasistant.data.local.dao

import androidx.room.*
import kotlinx.coroutines.flow.Flow
import me.fazlerabbie.mindasistant.data.local.entity.PaymentRecordEntity

@Dao
interface PaymentRecordDao {
    
    @Query("SELECT * FROM payment_records WHERE userId = :userId AND isDeleted = 0 ORDER BY paymentDate DESC")
    fun getPaymentRecordsFlow(userId: String): Flow<List<PaymentRecordEntity>>
    
    @Query("SELECT * FROM payment_records WHERE userId = :userId AND isDeleted = 0 ORDER BY paymentDate DESC")
    suspend fun getPaymentRecords(userId: String): List<PaymentRecordEntity>
    
    @Query("SELECT * FROM payment_records WHERE dueReminderId = :dueReminderId AND isDeleted = 0 ORDER BY paymentDate DESC")
    suspend fun getPaymentRecordsForReminder(dueReminderId: String): List<PaymentRecordEntity>
    
    @Query("SELECT * FROM payment_records WHERE id = :paymentId AND isDeleted = 0")
    suspend fun getPaymentRecordById(paymentId: String): PaymentRecordEntity?
    
    @Query("SELECT * FROM payment_records WHERE userId = :userId AND paymentDate >= :startDate AND paymentDate <= :endDate AND isDeleted = 0 ORDER BY paymentDate DESC")
    suspend fun getPaymentRecordsForDateRange(userId: String, startDate: Long, endDate: Long): List<PaymentRecordEntity>
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertPaymentRecord(paymentRecord: PaymentRecordEntity)
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertPaymentRecords(paymentRecords: List<PaymentRecordEntity>)
    
    @Update
    suspend fun updatePaymentRecord(paymentRecord: PaymentRecordEntity)
    
    @Query("UPDATE payment_records SET isDeleted = 1, isSynced = 0, lastModified = :lastModified WHERE id = :paymentId")
    suspend fun markPaymentRecordAsDeleted(paymentId: String, lastModified: Long)
    
    @Query("DELETE FROM payment_records WHERE id = :paymentId")
    suspend fun deletePaymentRecord(paymentId: String)
    
    @Query("DELETE FROM payment_records WHERE userId = :userId")
    suspend fun deleteAllPaymentRecordsForUser(userId: String)
    
    @Query("DELETE FROM payment_records WHERE dueReminderId = :dueReminderId")
    suspend fun deletePaymentRecordsForReminder(dueReminderId: String)
    
    // Sync related queries
    @Query("SELECT * FROM payment_records WHERE userId = :userId AND isSynced = 0")
    suspend fun getUnsyncedPaymentRecords(userId: String): List<PaymentRecordEntity>
    
    @Query("SELECT * FROM payment_records WHERE userId = :userId AND isDeleted = 1")
    suspend fun getDeletedPaymentRecords(userId: String): List<PaymentRecordEntity>
    
    @Query("UPDATE payment_records SET isSynced = 1 WHERE id = :paymentId")
    suspend fun markPaymentRecordAsSynced(paymentId: String)
    
    @Query("UPDATE payment_records SET isSynced = 1 WHERE id IN (:paymentIds)")
    suspend fun markPaymentRecordsAsSynced(paymentIds: List<String>)
    
    @Query("DELETE FROM payment_records WHERE isDeleted = 1 AND lastModified < :cutoffTime")
    suspend fun cleanupDeletedPaymentRecords(cutoffTime: Long)
    
    // Statistics queries
    @Query("SELECT SUM(amount) FROM payment_records WHERE userId = :userId AND isDeleted = 0")
    suspend fun getTotalPaymentAmount(userId: String): Double?
    
    @Query("SELECT SUM(amount) FROM payment_records WHERE dueReminderId = :dueReminderId AND isDeleted = 0")
    suspend fun getTotalPaymentAmountForReminder(dueReminderId: String): Double?
    
    @Query("SELECT COUNT(*) FROM payment_records WHERE userId = :userId AND isDeleted = 0")
    suspend fun getPaymentRecordCount(userId: String): Int
    
    @Query("SELECT SUM(amount) FROM payment_records WHERE userId = :userId AND paymentDate >= :startDate AND paymentDate <= :endDate AND isDeleted = 0")
    suspend fun getTotalPaymentAmountForDateRange(userId: String, startDate: Long, endDate: Long): Double?
}
