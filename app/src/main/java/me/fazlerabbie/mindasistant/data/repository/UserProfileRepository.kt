package me.fazlerabbie.mindasistant.data.repository

import android.content.Context
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import me.fazlerabbie.mindasistant.auth.SyncFrequency
import me.fazlerabbie.mindasistant.auth.UserProfile
import me.fazlerabbie.mindasistant.data.local.MindAssistantDatabase
import me.fazlerabbie.mindasistant.data.local.entity.toUserProfile
import me.fazlerabbie.mindasistant.data.local.entity.toUserProfileEntity

class UserProfileRepository(context: Context) {
    
    private val database = MindAssistantDatabase.getDatabase(context)
    private val userProfileDao = database.userProfileDao()
    
    /**
     * Get user profile flow for real-time updates (offline-first)
     */
    fun getUserProfileFlow(uid: String): Flow<UserProfile?> {
        return userProfileDao.getUserProfileFlow(uid).map { entity ->
            entity?.toUserProfile()
        }
    }
    
    /**
     * Get user profile (offline-first)
     */
    suspend fun getUserProfile(uid: String): UserProfile? {
        return userProfileDao.getUserProfile(uid)?.toUserProfile()
    }
    
    /**
     * Save user profile (offline-first)
     */
    suspend fun saveUserProfile(userProfile: UserProfile): Result<Unit> {
        return try {
            val profileEntity = userProfile.toUserProfileEntity(isSynced = false)
            userProfileDao.insertUserProfile(profileEntity)
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * Update user profile (offline-first)
     */
    suspend fun updateUserProfile(userProfile: UserProfile): Result<Unit> {
        return try {
            val profileEntity = userProfile.toUserProfileEntity(isSynced = false)
            userProfileDao.updateUserProfile(profileEntity)
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * Delete user profile (offline-first)
     */
    suspend fun deleteUserProfile(uid: String): Result<Unit> {
        return try {
            userProfileDao.deleteUserProfile(uid)
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * Update sync frequency (offline-first)
     */
    suspend fun updateSyncFrequency(uid: String, syncFrequency: SyncFrequency): Result<Unit> {
        return try {
            userProfileDao.updateSyncFrequency(uid, syncFrequency, System.currentTimeMillis())
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * Update auto sync enabled (offline-first)
     */
    suspend fun updateAutoSyncEnabled(uid: String, autoSyncEnabled: Boolean): Result<Unit> {
        return try {
            userProfileDao.updateAutoSyncEnabled(uid, autoSyncEnabled, System.currentTimeMillis())
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * Update sync only on WiFi (offline-first)
     */
    suspend fun updateSyncOnlyOnWifi(uid: String, syncOnlyOnWifi: Boolean): Result<Unit> {
        return try {
            userProfileDao.updateSyncOnlyOnWifi(uid, syncOnlyOnWifi, System.currentTimeMillis())
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * Update sync notifications enabled (offline-first)
     */
    suspend fun updateSyncNotificationsEnabled(uid: String, syncNotificationsEnabled: Boolean): Result<Unit> {
        return try {
            userProfileDao.updateSyncNotificationsEnabled(uid, syncNotificationsEnabled, System.currentTimeMillis())
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * Update last sync time
     */
    suspend fun updateLastSyncTime(uid: String, lastSyncTime: Long): Result<Unit> {
        return try {
            userProfileDao.updateLastSyncTime(uid, lastSyncTime)
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * Get sync preferences
     */
    suspend fun getSyncFrequency(uid: String): SyncFrequency? {
        return userProfileDao.getSyncFrequency(uid)
    }
    
    suspend fun getAutoSyncEnabled(uid: String): Boolean? {
        return userProfileDao.getAutoSyncEnabled(uid)
    }
    
    suspend fun getSyncOnlyOnWifi(uid: String): Boolean? {
        return userProfileDao.getSyncOnlyOnWifi(uid)
    }
    
    suspend fun getLastSyncTime(uid: String): Long? {
        return userProfileDao.getLastSyncTime(uid)
    }
    
    suspend fun getSyncNotificationsEnabled(uid: String): Boolean? {
        return userProfileDao.getSyncNotificationsEnabled(uid)
    }
}
