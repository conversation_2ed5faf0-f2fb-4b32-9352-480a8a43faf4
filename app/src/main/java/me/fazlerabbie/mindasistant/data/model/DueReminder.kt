package me.fazlerabbie.mindasistant.data.model

import java.util.*

data class DueReminder(
    val id: String = "",
    val userId: String = "",
    val name: String = "",
    val mobileNo: String = "",
    val dueAmount: Double = 0.0,
    val originalAmount: Double = 0.0,
    val dueDate: Long = System.currentTimeMillis(),
    val nextReminderDate: Long = System.currentTimeMillis(),
    val repeat: RepeatType = RepeatType.NONE,
    val note: String = "",
    val isCompleted: Boolean = false,
    val createdAt: Long = System.currentTimeMillis(),
    val updatedAt: Long = System.currentTimeMillis()
) {
    // No-argument constructor for Firestore
    constructor() : this("", "", "", "", 0.0, 0.0, System.currentTimeMillis(), System.currentTimeMillis(), RepeatType.NONE, "", false, System.currentTimeMillis(), System.currentTimeMillis())
    
    fun getRemainingAmount(): Double = dueAmount
    
    fun isOverdue(): Boolean {
        val today = Calendar.getInstance().apply {
            set(Calendar.HOUR_OF_DAY, 0)
            set(Calendar.MINUTE, 0)
            set(Calendar.SECOND, 0)
            set(Calendar.MILLISECOND, 0)
        }.timeInMillis
        
        return dueDate < today && !isCompleted
    }
    
    fun isDueToday(): Boolean {
        val today = Calendar.getInstance().apply {
            set(Calendar.HOUR_OF_DAY, 0)
            set(Calendar.MINUTE, 0)
            set(Calendar.SECOND, 0)
            set(Calendar.MILLISECOND, 0)
        }.timeInMillis
        
        val tomorrow = today + 24 * 60 * 60 * 1000
        
        return dueDate >= today && dueDate < tomorrow
    }
    
    fun isDueYesterday(): Boolean {
        val today = Calendar.getInstance().apply {
            set(Calendar.HOUR_OF_DAY, 0)
            set(Calendar.MINUTE, 0)
            set(Calendar.SECOND, 0)
            set(Calendar.MILLISECOND, 0)
        }.timeInMillis

        val yesterday = today - 24 * 60 * 60 * 1000

        return dueDate >= yesterday && dueDate < today
    }

    fun isUpcoming(): Boolean {
        val tomorrow = Calendar.getInstance().apply {
            add(Calendar.DAY_OF_MONTH, 1)
            set(Calendar.HOUR_OF_DAY, 0)
            set(Calendar.MINUTE, 0)
            set(Calendar.SECOND, 0)
            set(Calendar.MILLISECOND, 0)
        }.timeInMillis

        return dueDate >= tomorrow && !isCompleted
    }

    fun calculateNextReminderDate(): Long {
        if (isCompleted) return 0L

        val calendar = Calendar.getInstance()
        calendar.timeInMillis = dueDate

        return when (repeat) {
            RepeatType.DAILY -> {
                calendar.add(Calendar.DAY_OF_MONTH, 1)
                calendar.timeInMillis
            }
            RepeatType.WEEKLY -> {
                calendar.add(Calendar.WEEK_OF_YEAR, 1)
                calendar.timeInMillis
            }
            RepeatType.MONTHLY -> {
                calendar.add(Calendar.MONTH, 1)
                calendar.timeInMillis
            }
            RepeatType.YEARLY -> {
                calendar.add(Calendar.YEAR, 1)
                calendar.timeInMillis
            }
            RepeatType.NONE -> dueDate
        }
    }
}

enum class RepeatType(val displayName: String) {
    NONE("No Repeat"),
    DAILY("Daily"),
    WEEKLY("Weekly"),
    MONTHLY("Monthly"),
    YEARLY("Yearly")
}

enum class DueReminderFilter {
    TODAY,
    YESTERDAY,
    UPCOMING,
    COMPLETE,
    ALL
}
