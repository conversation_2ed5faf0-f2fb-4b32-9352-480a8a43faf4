package me.fazlerabbie.mindasistant.data.local

import androidx.room.Database
import androidx.room.Room
import androidx.room.RoomDatabase
import androidx.room.migration.Migration
import androidx.sqlite.db.SupportSQLiteDatabase
import android.content.Context
import me.fazlerabbie.mindasistant.data.local.dao.*
import me.fazlerabbie.mindasistant.data.local.entity.*

@Database(
    entities = [
        TodoEntity::class,
        DueReminderEntity::class,
        PaymentRecordEntity::class,
        UserProfileEntity::class
    ],
    version = 1,
    exportSchema = false
)
abstract class MindAssistantDatabase : RoomDatabase() {
    
    abstract fun todoDao(): TodoDao
    abstract fun dueReminderDao(): DueReminderDao
    abstract fun paymentRecordDao(): PaymentRecordDao
    abstract fun userProfileDao(): UserProfileDao
    
    companion object {
        @Volatile
        private var INSTANCE: MindAssistantDatabase? = null
        
        fun getDatabase(context: Context): MindAssistantDatabase {
            return INSTANCE ?: synchronized(this) {
                val instance = Room.databaseBuilder(
                    context.applicationContext,
                    MindAssistantDatabase::class.java,
                    "mind_assistant_database"
                )
                    .addMigrations(MIGRATION_1_2) // For future migrations
                    .fallbackToDestructiveMigration() // For development only
                    .build()
                INSTANCE = instance
                instance
            }
        }
        
        // Example migration for future use
        private val MIGRATION_1_2 = object : Migration(1, 2) {
            override fun migrate(database: SupportSQLiteDatabase) {
                // Migration logic will be added here when needed
                // For now, this is just a placeholder
            }
        }
    }
}
