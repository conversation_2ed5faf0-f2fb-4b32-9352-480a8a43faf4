package me.fazlerabbie.mindasistant.data.local.entity

import androidx.room.Entity
import androidx.room.PrimaryKey
import androidx.room.TypeConverter
import androidx.room.TypeConverters
import me.fazlerabbie.mindasistant.data.model.PaymentMethod
import me.fazlerabbie.mindasistant.data.model.PaymentRecord

@Entity(tableName = "payment_records")
@TypeConverters(PaymentRecordConverters::class)
data class PaymentRecordEntity(
    @PrimaryKey
    val id: String,
    val dueReminderId: String,
    val userId: String,
    val amount: Double,
    val paymentDate: Long,
    val note: String,
    val paymentMethod: PaymentMethod,
    val createdAt: Long,
    // Sync fields
    val isSynced: Boolean = false,
    val isDeleted: Boolean = false,
    val lastModified: Long = System.currentTimeMillis()
)

// Extension functions to convert between PaymentRecordEntity and PaymentRecord
fun PaymentRecordEntity.toPaymentRecord(): PaymentRecord {
    return PaymentRecord(
        id = id,
        dueReminderId = dueReminderId,
        userId = userId,
        amount = amount,
        paymentDate = paymentDate,
        note = note,
        paymentMethod = paymentMethod,
        createdAt = createdAt
    )
}

fun PaymentRecord.toPaymentRecordEntity(isSynced: Boolean = false, isDeleted: Boolean = false): PaymentRecordEntity {
    return PaymentRecordEntity(
        id = id,
        dueReminderId = dueReminderId,
        userId = userId,
        amount = amount,
        paymentDate = paymentDate,
        note = note,
        paymentMethod = paymentMethod,
        createdAt = createdAt,
        isSynced = isSynced,
        isDeleted = isDeleted,
        lastModified = System.currentTimeMillis()
    )
}

class PaymentRecordConverters {
    @TypeConverter
    fun fromPaymentMethod(paymentMethod: PaymentMethod): String {
        return paymentMethod.name
    }

    @TypeConverter
    fun toPaymentMethod(paymentMethod: String): PaymentMethod {
        return PaymentMethod.valueOf(paymentMethod)
    }
}
