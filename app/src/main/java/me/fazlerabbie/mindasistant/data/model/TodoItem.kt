package me.fazlerabbie.mindasistant.data.model

import java.util.*

data class TodoItem(
    val id: String = "",
    val userId: String = "",
    val title: String = "",
    val description: String = "",
    val isCompleted: Boolean = false,
    val priority: TodoPriority = TodoPriority.MEDIUM,
    val category: TodoCategory = TodoCategory.PERSONAL,
    val dueDate: Long? = null,
    val reminderDate: Long? = null,
    val tags: List<String> = emptyList(),
    val createdAt: Long = System.currentTimeMillis(),
    val updatedAt: Long = System.currentTimeMillis(),
    val completedAt: Long? = null
) {
    // No-argument constructor for Firestore
    constructor() : this(
        "", "", "", "", false, TodoPriority.MEDIUM, TodoCategory.PERSONAL,
        null, null, emptyList(), System.currentTimeMillis(), System.currentTimeMillis(), null
    )
    
    fun isOverdue(): Boolean {
        if (dueDate == null || isCompleted) return false
        return dueDate < System.currentTimeMillis()
    }
    
    fun isDueToday(): Boolean {
        if (dueDate == null) return false
        
        val today = Calendar.getInstance().apply {
            set(Calendar.HOUR_OF_DAY, 0)
            set(Calendar.MINUTE, 0)
            set(Calendar.SECOND, 0)
            set(Calendar.MILLISECOND, 0)
        }.timeInMillis
        
        val tomorrow = today + 24 * 60 * 60 * 1000
        
        return dueDate >= today && dueDate < tomorrow
    }
    
    fun isDueTomorrow(): Boolean {
        if (dueDate == null) return false
        
        val tomorrow = Calendar.getInstance().apply {
            add(Calendar.DAY_OF_MONTH, 1)
            set(Calendar.HOUR_OF_DAY, 0)
            set(Calendar.MINUTE, 0)
            set(Calendar.SECOND, 0)
            set(Calendar.MILLISECOND, 0)
        }.timeInMillis
        
        val dayAfterTomorrow = tomorrow + 24 * 60 * 60 * 1000
        
        return dueDate >= tomorrow && dueDate < dayAfterTomorrow
    }
    
    fun isUpcoming(): Boolean {
        if (dueDate == null || isCompleted) return false
        return dueDate > System.currentTimeMillis()
    }
    
    fun getDaysUntilDue(): Int? {
        if (dueDate == null) return null
        
        val today = Calendar.getInstance().apply {
            set(Calendar.HOUR_OF_DAY, 0)
            set(Calendar.MINUTE, 0)
            set(Calendar.SECOND, 0)
            set(Calendar.MILLISECOND, 0)
        }.timeInMillis
        
        val daysInMillis = 24 * 60 * 60 * 1000
        return ((dueDate - today) / daysInMillis).toInt()
    }
}

enum class TodoPriority(val displayName: String, val colorValue: Long) {
    LOW("Low", 0xFF16A34A),
    MEDIUM("Medium", 0xFFEA580C),
    HIGH("High", 0xFFDC2626),
    URGENT("Urgent", 0xFF7C2D12)
}

enum class TodoCategory(val displayName: String, val colorValue: Long) {
    PERSONAL("Personal", 0xFF2563EB),
    WORK("Work", 0xFF7C3AED),
    SHOPPING("Shopping", 0xFF059669),
    HEALTH("Health", 0xFFDC2626),
    EDUCATION("Education", 0xFFDB2777),
    FINANCE("Finance", 0xFFEA580C),
    TRAVEL("Travel", 0xFF0891B2),
    OTHER("Other", 0xFF6B7280)
}

enum class TodoFilter {
    ALL,
    PENDING,
    COMPLETED,
    TODAY,
    TOMORROW,
    OVERDUE,
    UPCOMING,
    HIGH_PRIORITY,
    MEDIUM_PRIORITY,
    LOW_PRIORITY
}

enum class TodoSortBy {
    CREATED_DATE,
    DUE_DATE,
    PRIORITY,
    TITLE,
    CATEGORY
}

enum class SortOrder {
    ASCENDING,
    DESCENDING
}
