package me.fazlerabbie.mindasistant.data.local.entity

import androidx.room.Entity
import androidx.room.PrimaryKey
import androidx.room.TypeConverter
import androidx.room.TypeConverters
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import me.fazlerabbie.mindasistant.data.model.TodoCategory
import me.fazlerabbie.mindasistant.data.model.TodoItem
import me.fazlerabbie.mindasistant.data.model.TodoPriority

@Entity(tableName = "todos")
@TypeConverters(TodoConverters::class)
data class TodoEntity(
    @PrimaryKey
    val id: String,
    val userId: String,
    val title: String,
    val description: String,
    val isCompleted: Boolean,
    val priority: TodoPriority,
    val category: TodoCategory,
    val dueDate: Long?,
    val reminderDate: Long?,
    val tags: List<String>,
    val createdAt: Long,
    val updatedAt: Long,
    val completedAt: Long?,
    // Sync fields
    val isSynced: Boolean = false,
    val isDeleted: Boolean = false,
    val lastModified: Long = System.currentTimeMillis()
)

// Extension functions to convert between TodoEntity and TodoItem
fun TodoEntity.toTodoItem(): TodoItem {
    return TodoItem(
        id = id,
        userId = userId,
        title = title,
        description = description,
        isCompleted = isCompleted,
        priority = priority,
        category = category,
        dueDate = dueDate,
        reminderDate = reminderDate,
        tags = tags,
        createdAt = createdAt,
        updatedAt = updatedAt,
        completedAt = completedAt
    )
}

fun TodoItem.toTodoEntity(isSynced: Boolean = false, isDeleted: Boolean = false): TodoEntity {
    return TodoEntity(
        id = id,
        userId = userId,
        title = title,
        description = description,
        isCompleted = isCompleted,
        priority = priority,
        category = category,
        dueDate = dueDate,
        reminderDate = reminderDate,
        tags = tags,
        createdAt = createdAt,
        updatedAt = updatedAt,
        completedAt = completedAt,
        isSynced = isSynced,
        isDeleted = isDeleted,
        lastModified = System.currentTimeMillis()
    )
}

class TodoConverters {
    @TypeConverter
    fun fromStringList(value: List<String>): String {
        return Gson().toJson(value)
    }

    @TypeConverter
    fun toStringList(value: String): List<String> {
        val listType = object : TypeToken<List<String>>() {}.type
        return Gson().fromJson(value, listType) ?: emptyList()
    }

    @TypeConverter
    fun fromTodoPriority(priority: TodoPriority): String {
        return priority.name
    }

    @TypeConverter
    fun toTodoPriority(priority: String): TodoPriority {
        return TodoPriority.valueOf(priority)
    }

    @TypeConverter
    fun fromTodoCategory(category: TodoCategory): String {
        return category.name
    }

    @TypeConverter
    fun toTodoCategory(category: String): TodoCategory {
        return TodoCategory.valueOf(category)
    }
}
