package me.fazlerabbie.mindasistant.data.repository

import android.content.Context
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import me.fazlerabbie.mindasistant.data.local.MindAssistantDatabase
import me.fazlerabbie.mindasistant.data.local.entity.*
import me.fazlerabbie.mindasistant.firestore.DueReminderService
import me.fazlerabbie.mindasistant.firestore.TodoService
import me.fazlerabbie.mindasistant.firestore.UserProfileService

class SyncRepository(context: Context) {
    
    private val database = MindAssistantDatabase.getDatabase(context)
    private val todoService = TodoService()
    private val dueReminderService = DueReminderService()
    private val userProfileService = UserProfileService()
    
    /**
     * Perform full sync for a user
     */
    suspend fun performFullSync(userId: String): Result<Unit> = withContext(Dispatchers.IO) {
        try {
            // Sync user profile first
            syncUserProfile(userId)
            
            // Sync todos
            syncTodos(userId)
            
            // Sync due reminders
            syncDueReminders(userId)
            
            // Sync payment records
            syncPaymentRecords(userId)
            
            // Clean up old deleted records
            cleanupDeletedRecords()
            
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * Sync user profile
     */
    private suspend fun syncUserProfile(userId: String) {
        // Upload local changes to Firebase
        val unsyncedProfiles = database.userProfileDao().getUnsyncedUserProfiles()
        unsyncedProfiles.forEach { profileEntity ->
            val userProfile = profileEntity.toUserProfile()
            userProfileService.saveUserProfile(userProfile).fold(
                onSuccess = {
                    database.userProfileDao().markUserProfileAsSynced(profileEntity.uid)
                },
                onFailure = { /* Handle error - could retry later */ }
            )
        }
        
        // Download changes from Firebase
        userProfileService.getUserProfile(userId).fold(
            onSuccess = { remoteProfile ->
                remoteProfile?.let { profile ->
                    val localProfile = database.userProfileDao().getUserProfile(userId)
                    if (localProfile == null || profile.createdAt > localProfile.lastModified) {
                        database.userProfileDao().insertUserProfile(
                            profile.toUserProfileEntity(isSynced = true)
                        )
                    }
                }
            },
            onFailure = { /* Handle error */ }
        )
    }
    
    /**
     * Sync todos
     */
    private suspend fun syncTodos(userId: String) {
        // Upload local changes to Firebase
        val unsyncedTodos = database.todoDao().getUnsyncedTodos(userId)
        unsyncedTodos.forEach { todoEntity ->
            if (todoEntity.isDeleted) {
                // Delete from Firebase
                todoService.deleteTodoItem(todoEntity.id).fold(
                    onSuccess = {
                        database.todoDao().deleteTodo(todoEntity.id)
                    },
                    onFailure = { /* Handle error */ }
                )
            } else {
                // Upload to Firebase
                val todoItem = todoEntity.toTodoItem()
                todoService.saveTodoItem(todoItem).fold(
                    onSuccess = {
                        database.todoDao().markTodoAsSynced(todoEntity.id)
                    },
                    onFailure = { /* Handle error */ }
                )
            }
        }
        
        // Download changes from Firebase
        todoService.getTodos(userId).fold(
            onSuccess = { remoteTodos ->
                remoteTodos.forEach { remoteTodo ->
                    val localTodo = database.todoDao().getTodoById(remoteTodo.id)
                    if (localTodo == null || remoteTodo.updatedAt > localTodo.lastModified) {
                        database.todoDao().insertTodo(
                            remoteTodo.toTodoEntity(isSynced = true)
                        )
                    }
                }
            },
            onFailure = { /* Handle error */ }
        )
    }
    
    /**
     * Sync due reminders
     */
    private suspend fun syncDueReminders(userId: String) {
        // Upload local changes to Firebase
        val unsyncedReminders = database.dueReminderDao().getUnsyncedReminders(userId)
        unsyncedReminders.forEach { reminderEntity ->
            if (reminderEntity.isDeleted) {
                // Delete from Firebase
                dueReminderService.deleteDueReminder(reminderEntity.id).fold(
                    onSuccess = {
                        database.dueReminderDao().deleteDueReminder(reminderEntity.id)
                    },
                    onFailure = { /* Handle error */ }
                )
            } else {
                // Upload to Firebase
                val reminder = reminderEntity.toDueReminder()
                dueReminderService.saveDueReminder(reminder).fold(
                    onSuccess = {
                        database.dueReminderDao().markReminderAsSynced(reminderEntity.id)
                    },
                    onFailure = { /* Handle error */ }
                )
            }
        }
        
        // Download changes from Firebase
        dueReminderService.getDueReminders(userId).fold(
            onSuccess = { remoteReminders ->
                remoteReminders.forEach { remoteReminder ->
                    val localReminder = database.dueReminderDao().getDueReminderById(remoteReminder.id)
                    if (localReminder == null || remoteReminder.updatedAt > localReminder.lastModified) {
                        database.dueReminderDao().insertDueReminder(
                            remoteReminder.toDueReminderEntity(isSynced = true)
                        )
                    }
                }
            },
            onFailure = { /* Handle error */ }
        )
    }
    
    /**
     * Sync payment records
     */
    private suspend fun syncPaymentRecords(userId: String) {
        // Upload local changes to Firebase
        val unsyncedPayments = database.paymentRecordDao().getUnsyncedPaymentRecords(userId)
        unsyncedPayments.forEach { paymentEntity ->
            if (paymentEntity.isDeleted) {
                // Delete from Firebase
                dueReminderService.deletePaymentRecord(paymentEntity.id).fold(
                    onSuccess = {
                        database.paymentRecordDao().deletePaymentRecord(paymentEntity.id)
                    },
                    onFailure = { /* Handle error */ }
                )
            } else {
                // Upload to Firebase
                val payment = paymentEntity.toPaymentRecord()
                dueReminderService.savePaymentRecord(payment).fold(
                    onSuccess = {
                        database.paymentRecordDao().markPaymentRecordAsSynced(paymentEntity.id)
                    },
                    onFailure = { /* Handle error */ }
                )
            }
        }
        
        // Download changes from Firebase
        dueReminderService.getPaymentRecords(userId).fold(
            onSuccess = { remotePayments ->
                remotePayments.forEach { remotePayment ->
                    val localPayment = database.paymentRecordDao().getPaymentRecordById(remotePayment.id)
                    if (localPayment == null || remotePayment.createdAt > (localPayment?.lastModified ?: 0)) {
                        database.paymentRecordDao().insertPaymentRecord(
                            remotePayment.toPaymentRecordEntity(isSynced = true)
                        )
                    }
                }
            },
            onFailure = { /* Handle error */ }
        )
    }
    
    /**
     * Clean up old deleted records (older than 30 days)
     */
    private suspend fun cleanupDeletedRecords() {
        val cutoffTime = System.currentTimeMillis() - (30 * 24 * 60 * 60 * 1000L) // 30 days ago
        
        database.todoDao().cleanupDeletedTodos(cutoffTime)
        database.dueReminderDao().cleanupDeletedReminders(cutoffTime)
        database.paymentRecordDao().cleanupDeletedPaymentRecords(cutoffTime)
    }
}
