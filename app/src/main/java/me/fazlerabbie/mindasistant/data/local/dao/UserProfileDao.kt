package me.fazlerabbie.mindasistant.data.local.dao

import androidx.room.*
import kotlinx.coroutines.flow.Flow
import me.fazlerabbie.mindasistant.auth.SyncFrequency
import me.fazlerabbie.mindasistant.data.local.entity.UserProfileEntity

@Dao
interface UserProfileDao {
    
    @Query("SELECT * FROM user_profiles WHERE uid = :uid")
    suspend fun getUserProfile(uid: String): UserProfileEntity?
    
    @Query("SELECT * FROM user_profiles WHERE uid = :uid")
    fun getUserProfileFlow(uid: String): Flow<UserProfileEntity?>
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertUserProfile(userProfile: UserProfileEntity)
    
    @Update
    suspend fun updateUserProfile(userProfile: UserProfileEntity)
    
    @Query("DELETE FROM user_profiles WHERE uid = :uid")
    suspend fun deleteUserProfile(uid: String)
    
    @Query("DELETE FROM user_profiles")
    suspend fun deleteAllUserProfiles()
    
    // Sync preference updates
    @Query("UPDATE user_profiles SET syncFrequency = :syncFrequency, isSynced = 0, lastModified = :lastModified WHERE uid = :uid")
    suspend fun updateSyncFrequency(uid: String, syncFrequency: SyncFrequency, lastModified: Long)
    
    @Query("UPDATE user_profiles SET autoSyncEnabled = :autoSyncEnabled, isSynced = 0, lastModified = :lastModified WHERE uid = :uid")
    suspend fun updateAutoSyncEnabled(uid: String, autoSyncEnabled: Boolean, lastModified: Long)
    
    @Query("UPDATE user_profiles SET syncOnlyOnWifi = :syncOnlyOnWifi, isSynced = 0, lastModified = :lastModified WHERE uid = :uid")
    suspend fun updateSyncOnlyOnWifi(uid: String, syncOnlyOnWifi: Boolean, lastModified: Long)
    
    @Query("UPDATE user_profiles SET syncNotificationsEnabled = :syncNotificationsEnabled, isSynced = 0, lastModified = :lastModified WHERE uid = :uid")
    suspend fun updateSyncNotificationsEnabled(uid: String, syncNotificationsEnabled: Boolean, lastModified: Long)
    
    @Query("UPDATE user_profiles SET lastSyncTime = :lastSyncTime WHERE uid = :uid")
    suspend fun updateLastSyncTime(uid: String, lastSyncTime: Long)
    
    // Sync related queries
    @Query("SELECT * FROM user_profiles WHERE isSynced = 0")
    suspend fun getUnsyncedUserProfiles(): List<UserProfileEntity>
    
    @Query("UPDATE user_profiles SET isSynced = 1 WHERE uid = :uid")
    suspend fun markUserProfileAsSynced(uid: String)
    
    // Get sync preferences
    @Query("SELECT syncFrequency FROM user_profiles WHERE uid = :uid")
    suspend fun getSyncFrequency(uid: String): SyncFrequency?
    
    @Query("SELECT autoSyncEnabled FROM user_profiles WHERE uid = :uid")
    suspend fun getAutoSyncEnabled(uid: String): Boolean?
    
    @Query("SELECT syncOnlyOnWifi FROM user_profiles WHERE uid = :uid")
    suspend fun getSyncOnlyOnWifi(uid: String): Boolean?
    
    @Query("SELECT lastSyncTime FROM user_profiles WHERE uid = :uid")
    suspend fun getLastSyncTime(uid: String): Long?
    
    @Query("SELECT syncNotificationsEnabled FROM user_profiles WHERE uid = :uid")
    suspend fun getSyncNotificationsEnabled(uid: String): Boolean?
}
