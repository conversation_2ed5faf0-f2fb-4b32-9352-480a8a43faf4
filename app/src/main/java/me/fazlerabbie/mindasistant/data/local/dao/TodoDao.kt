package me.fazlerabbie.mindasistant.data.local.dao

import androidx.room.*
import kotlinx.coroutines.flow.Flow
import me.fazlerabbie.mindasistant.data.local.entity.TodoEntity

@Dao
interface TodoDao {
    
    @Query("SELECT * FROM todos WHERE userId = :userId AND isDeleted = 0 ORDER BY createdAt DESC")
    fun getTodosFlow(userId: String): Flow<List<TodoEntity>>
    
    @Query("SELECT * FROM todos WHERE userId = :userId AND isDeleted = 0 ORDER BY createdAt DESC")
    suspend fun getTodos(userId: String): List<TodoEntity>
    
    @Query("SELECT * FROM todos WHERE id = :todoId AND isDeleted = 0")
    suspend fun getTodoById(todoId: String): TodoEntity?
    
    @Query("SELECT * FROM todos WHERE userId = :userId AND isCompleted = :isCompleted AND isDeleted = 0 ORDER BY createdAt DESC")
    suspend fun getTodosByCompletion(userId: String, isCompleted: Boolean): List<TodoEntity>
    
    @Query("SELECT * FROM todos WHERE userId = :userId AND dueDate IS NOT NULL AND dueDate <= :currentTime AND isCompleted = 0 AND isDeleted = 0 ORDER BY dueDate ASC")
    suspend fun getOverdueTodos(userId: String, currentTime: Long): List<TodoEntity>
    
    @Query("SELECT * FROM todos WHERE userId = :userId AND dueDate IS NOT NULL AND dueDate > :startOfDay AND dueDate < :endOfDay AND isCompleted = 0 AND isDeleted = 0 ORDER BY dueDate ASC")
    suspend fun getTodosForDate(userId: String, startOfDay: Long, endOfDay: Long): List<TodoEntity>
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertTodo(todo: TodoEntity)
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertTodos(todos: List<TodoEntity>)
    
    @Update
    suspend fun updateTodo(todo: TodoEntity)
    
    @Query("UPDATE todos SET isCompleted = :isCompleted, completedAt = :completedAt, updatedAt = :updatedAt, isSynced = 0, lastModified = :lastModified WHERE id = :todoId")
    suspend fun updateTodoCompletion(
        todoId: String, 
        isCompleted: Boolean, 
        completedAt: Long?, 
        updatedAt: Long,
        lastModified: Long
    )
    
    @Query("UPDATE todos SET isDeleted = 1, isSynced = 0, lastModified = :lastModified WHERE id = :todoId")
    suspend fun markTodoAsDeleted(todoId: String, lastModified: Long)
    
    @Query("DELETE FROM todos WHERE id = :todoId")
    suspend fun deleteTodo(todoId: String)
    
    @Query("DELETE FROM todos WHERE userId = :userId")
    suspend fun deleteAllTodosForUser(userId: String)
    
    // Sync related queries
    @Query("SELECT * FROM todos WHERE userId = :userId AND isSynced = 0")
    suspend fun getUnsyncedTodos(userId: String): List<TodoEntity>
    
    @Query("SELECT * FROM todos WHERE userId = :userId AND isDeleted = 1")
    suspend fun getDeletedTodos(userId: String): List<TodoEntity>
    
    @Query("UPDATE todos SET isSynced = 1 WHERE id = :todoId")
    suspend fun markTodoAsSynced(todoId: String)
    
    @Query("UPDATE todos SET isSynced = 1 WHERE id IN (:todoIds)")
    suspend fun markTodosAsSynced(todoIds: List<String>)
    
    @Query("DELETE FROM todos WHERE isDeleted = 1 AND lastModified < :cutoffTime")
    suspend fun cleanupDeletedTodos(cutoffTime: Long)
    
    @Query("SELECT COUNT(*) FROM todos WHERE userId = :userId AND isDeleted = 0")
    suspend fun getTodoCount(userId: String): Int
    
    @Query("SELECT COUNT(*) FROM todos WHERE userId = :userId AND isCompleted = 0 AND isDeleted = 0")
    suspend fun getPendingTodoCount(userId: String): Int
    
    @Query("SELECT COUNT(*) FROM todos WHERE userId = :userId AND isCompleted = 1 AND isDeleted = 0")
    suspend fun getCompletedTodoCount(userId: String): Int
}
